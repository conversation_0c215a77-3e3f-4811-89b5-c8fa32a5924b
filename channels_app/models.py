from django.db import models
from django.conf import settings
from django.utils.text import slugify
from home.models import BaseModel
from organizations.models import Organization
from mycorrmessaging.modules.choices import (
    CHANNEL_TYPE_CHOICES, CHANNEL_MEMBER_ROLE_CHOICES
)


class Channel(BaseModel):
    """
    Model representing a communication channel within an organization.
    """
    name = models.CharField(
        max_length=255,
        help_text="Channel name"
    )
    slug = models.SlugField(
        max_length=255,
        help_text="URL-friendly channel identifier"
    )
    parent_channel = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='group_channels',
        help_text="Parent channel for group channels"
    )
    description = models.TextField(
        null=True,
        blank=True,
        help_text="Channel description"
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='channels',
        help_text="Organization this channel belongs to"
    )
    channel_type = models.Cha<PERSON><PERSON><PERSON>(
        max_length=20,
        choices=CHANNEL_TYPE_CHOICES,
        default='public',
        help_text="Channel type (public, private, direct)"
    )
    is_active = models.Bo<PERSON>anField(
        default=True,
        help_text="Whether channel is active"
    )
    is_archived = models.BooleanField(
        default=False,
        help_text="Whether channel is archived"
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_channels',
        help_text="User who created this channel"
    )

    # Channel settings
    is_default = models.BooleanField(
        default=False,
        help_text="Whether this is a default channel for the organization"
    )
    allow_threads = models.BooleanField(
        default=True,
        help_text="Allow threaded replies in this channel"
    )
    allow_file_uploads = models.BooleanField(
        default=True,
        help_text="Allow file uploads in this channel"
    )
    max_members = models.PositiveIntegerField(
        null=True,
        blank=True,
        default=10000,
        help_text="Maximum number of members (null for unlimited)"
    )

    participants = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        through='ChannelMembership',
        through_fields=('channel', 'user'),
        related_name='channels',
        help_text="Channel participants"
    )

    class Meta:
        db_table = 'channels_channel'
        verbose_name = 'Channel'
        verbose_name_plural = 'Channels'
        unique_together = ['organization', 'slug']
        ordering = ['name']

    def __str__(self):
        if self.channel_type == 'direct':
            participant_names = [p.get_full_name() for p in self.participants.all()[:2]]
            return f"DM: {' & '.join(participant_names)}"
        return f"#{self.name}"

    @property
    def member_count(self):
        """Get the number of active members in the channel."""
        return self.memberships.filter(is_active=True).count()

    @property
    def is_group_channel(self):
        """Check if channel is a group channel."""
        return self.parent_channel is not None

    @property
    def is_at_capacity(self):
        """Check if channel is at maximum capacity."""
        if self.max_members is None:
            return False
        return self.member_count >= self.max_members

    def get_member_role(self, user):
        """Get the role of a user in this channel."""
        try:
            membership = self.memberships.get(user=user, is_active=True)
            return membership.role
        except ChannelMembership.DoesNotExist:
            return None

    def is_member(self, user):
        """Check if user is a member of this channel."""
        return self.memberships.filter(user=user, is_active=True).exists()

    def is_admin(self, user):
        """Check if user is an admin of this channel."""
        return self.memberships.filter(
            user=user,
            is_active=True,
            role__in=['owner', 'admin']
        ).exists()

    def can_user_access(self, user):
        """Check if user can access this channel."""
        # Check if user is member of the organization
        if not self.organization.is_member(user):
            return False

        # Public channels are accessible to all organization members
        if self.channel_type == 'public':
            return True

        # Private and direct channels require membership
        return self.is_member(user)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
            # Ensure unique slug within organization
            counter = 1
            original_slug = self.slug
            while Channel.objects.filter(
                organization=self.organization,
                slug=self.slug
            ).exclude(id=self.id).exists():
                self.slug = f"{original_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)


class ChannelMembership(BaseModel):
    """
    Model representing a user's membership in a channel.
    """
    channel = models.ForeignKey(
        Channel,
        on_delete=models.CASCADE,
        related_name='memberships',
        help_text="Channel"
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='channel_memberships',
        help_text="User"
    )
    role = models.CharField(
        max_length=20,
        choices=CHANNEL_MEMBER_ROLE_CHOICES,
        default='member',
        help_text="User's role in the channel"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether membership is active"
    )
    joined_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When user joined the channel"
    )
    invited_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sent_channel_invitations',
        help_text="User who invited this member"
    )

    # Notification settings
    mute_notifications = models.BooleanField(
        default=False,
        help_text="Whether notifications are muted for this channel"
    )
    last_read_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Last time user read messages in this channel"
    )

    class Meta:
        db_table = 'channels_membership'
        verbose_name = 'Channel Membership'
        verbose_name_plural = 'Channel Memberships'
        unique_together = ['channel', 'user']
        ordering = ['-joined_at']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.channel} ({self.role})"
