from django.contrib import admin
from django.utils.html import format_html
from .models import Channel, ChannelMembership


class ChannelMembershipInline(admin.TabularInline):
    """Inline admin for channel memberships."""
    model = ChannelMembership
    extra = 0
    readonly_fields = ['joined_at', 'last_read_at']
    fields = ['user', 'role', 'is_active', 'joined_at', 'invited_by', 'mute_notifications']


@admin.register(Channel)
class ChannelAdmin(admin.ModelAdmin):
    """Admin configuration for Channel model."""

    list_display = [
        'name', 'organization', 'channel_type', 'member_count',
        'is_active', 'is_archived', 'is_default', 'created_at'
    ]
    list_filter = [
        'channel_type', 'is_active', 'is_archived', 'is_default',
        'allow_threads', 'allow_file_uploads', 'created_at'
    ]
    search_fields = ['name', 'slug', 'description', 'organization__name']
    readonly_fields = [
        'id', 'slug', 'member_count', 'is_at_capacity', 'created_at', 'updated_at'
    ]
    prepopulated_fields = {'slug': ('name',)}
    inlines = [ChannelMembershipInline]

    fieldsets = (
        (None, {
            'fields': ('name', 'slug', 'description', 'organization', 'channel_type')
        }),
        ('Settings', {
            'fields': (
                'is_active', 'is_archived', 'is_default',
                'allow_threads', 'allow_file_uploads', 'max_members'
            )
        }),
        ('Statistics', {
            'fields': ('member_count', 'is_at_capacity')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('organization', 'created_by')


@admin.register(ChannelMembership)
class ChannelMembershipAdmin(admin.ModelAdmin):
    """Admin configuration for ChannelMembership model."""

    list_display = [
        'user', 'channel', 'role', 'is_active', 'mute_notifications', 'joined_at'
    ]
    list_filter = ['role', 'is_active', 'mute_notifications', 'joined_at']
    search_fields = [
        'user__email', 'user__first_name', 'user__last_name',
        'channel__name', 'channel__organization__name'
    ]
    readonly_fields = ['id', 'joined_at', 'last_read_at', 'created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('channel', 'user', 'role', 'is_active')
        }),
        ('Settings', {
            'fields': ('mute_notifications', 'last_read_at')
        }),
        ('Invitation Info', {
            'fields': ('invited_by',)
        }),
        ('Timestamps', {
            'fields': ('joined_at', 'created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'channel', 'channel__organization', 'invited_by'
        )
