from django.urls import path
from . import views

app_name = 'channels'

urlpatterns = [
    # Channel management
    path('create/', views.ChannelCreateAPIView.as_view(), name='create'),
    path('direct-message/', views.DirectMessageCreateAPIView.as_view(), name='create_dm'),
    
    # Organization channels
    path('org/<slug:org_slug>/', views.OrganizationChannelsAPIView.as_view(), name='org_channels'),
    path('org/<slug:org_slug>/<slug:channel_slug>/', views.ChannelDetailAPIView.as_view(), name='detail'),
    path('org/<slug:org_slug>/<slug:channel_slug>/join/', views.JoinChannelAPIView.as_view(), name='join'),
    path('org/<slug:org_slug>/<slug:channel_slug>/leave/', views.LeaveChannelAPIView.as_view(), name='leave'),
    
    # Channel members
    path('org/<slug:org_slug>/<slug:channel_slug>/members/', views.ChannelMembersAPIView.as_view(), name='members'),
    path('org/<slug:org_slug>/<slug:channel_slug>/members/<uuid:user_id>/', views.ChannelMemberDetailAPIView.as_view(), name='member_detail'),
    path('org/<slug:org_slug>/<slug:channel_slug>/invite/', views.ChannelInviteAPIView.as_view(), name='invite'),
]
