from rest_framework import status, generics, permissions, serializers
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.db.models import Q
from drf_spectacular.utils import extend_schema
from mycorrmessaging.modules.exceptions import raise_serializer_error_msg, InvalidRequestException
from mycorrmessaging.modules.paginations import CustomPagination
from organizations.models import Organization
from .models import Channel, ChannelMembership
from .serializers import (
    ChannelCreateSerializerIn, ChannelUpdateSerializerIn,
    ChannelSerializerOut, ChannelListSerializerOut,
    ChannelMembershipSerializerOut, ChannelMembershipUpdateSerializerIn,
    ChannelInviteSerializerIn, DirectMessageCreateSerializerIn,
    JoinChannelSerializerOut, LeaveChannelSerializerOut
)

User = get_user_model()


class ChannelCreateAPIView(APIView):
    """API view for creating channels."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=ChannelCreateSerializerIn,
        responses={status.HTTP_201_CREATED: ChannelSerializerOut}
    )
    def post(self, request):
        serializer = ChannelCreateSerializerIn(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        channel = serializer.save()
        response_data = ChannelSerializerOut(
            channel,
            context={'request': request}
        ).data

        return Response({"message": "Channel created successfully", "data": response_data}, status=status.HTTP_201_CREATED)


class OrganizationChannelsAPIView(generics.ListAPIView):
    """API view for listing organization channels."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = ChannelListSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        org_slug = self.kwargs['org_slug']
        organization = get_object_or_404(Organization, slug=org_slug, is_active=True)

        if not organization.is_member(self.request.user):
            raise InvalidRequestException({"message": "You are not a member of this organization."})

        # Return channels user can access
        user = self.request.user
        return Channel.objects.filter(
            Q(organization=organization) &
            Q(is_active=True) &
            (
                Q(channel_type='public') |  # Public channels
                Q(memberships__user=user, memberships__is_active=True)  # Private channels user is member of
            )
        ).distinct().order_by('name')


class ChannelDetailAPIView(APIView):
    """API view for channel details."""
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self, org_slug, channel_slug):
        organization = get_object_or_404(Organization, slug=org_slug, is_active=True)
        channel = get_object_or_404(
            Channel,
            organization=organization,
            slug=channel_slug,
            is_active=True
        )

        if not channel.can_user_access(self.request.user):
            raise InvalidRequestException({"message": "You don't have access to this channel."})

        return channel

    @extend_schema(responses={status.HTTP_200_OK: ChannelSerializerOut})
    def get(self, request, org_slug, channel_slug):
        """Get channel details."""
        channel = self.get_object(org_slug, channel_slug)
        serializer = ChannelSerializerOut(
            channel,
            context={'request': request}
        )
        return Response(serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        request=ChannelUpdateSerializerIn,
        responses={status.HTTP_200_OK: ChannelSerializerOut}
    )
    def patch(self, request, org_slug, channel_slug):
        """Update channel details."""
        channel = self.get_object(org_slug, channel_slug)

        # Check if user is admin
        if not channel.is_admin(request.user):
            raise InvalidRequestException({"message": "You don't have permission to update this channel."})

        serializer = ChannelUpdateSerializerIn(
            channel,
            data=request.data,
            partial=True
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        channel = serializer.save()
        response_data = ChannelSerializerOut(
            channel,
            context={'request': request}
        ).data
        response_data['message'] = 'Channel updated successfully'

        return Response(response_data, status=status.HTTP_200_OK)

    @extend_schema(responses={status.HTTP_204_NO_CONTENT})
    def delete(self, request, org_slug, channel_slug):
        """Delete/archive channel."""
        channel = self.get_object(org_slug, channel_slug)

        # Only owner can delete channel
        membership = channel.memberships.get(user=request.user, is_active=True)
        if membership.role != 'owner':
            raise InvalidRequestException({"message": "Only channel owner can delete the channel."})

        # Don't allow deleting default channels
        if channel.is_default:
            raise InvalidRequestException({"message": "Cannot delete default channel."})

        channel.is_active = False
        channel.save()

        return Response(
            {'message': 'Channel deleted successfully'},
            status=status.HTTP_204_NO_CONTENT
        )


class ChannelMembersAPIView(generics.ListAPIView):
    """API view for listing channel members."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = ChannelMembershipSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        org_slug = self.kwargs['org_slug']
        channel_slug = self.kwargs['channel_slug']

        organization = get_object_or_404(Organization, slug=org_slug, is_active=True)
        channel = get_object_or_404(
            Channel,
            organization=organization,
            slug=channel_slug,
            is_active=True
        )

        if not channel.can_user_access(self.request.user):
            raise InvalidRequestException({"message": "You don't have access to this channel."})

        return channel.memberships.filter(is_active=True).select_related('user', 'invited_by')


class ChannelMemberDetailAPIView(APIView):
    """API view for managing channel member details."""
    permission_classes = [permissions.IsAuthenticated]

    def get_objects(self, org_slug, channel_slug, user_id):
        organization = get_object_or_404(Organization, slug=org_slug, is_active=True)
        channel = get_object_or_404(
            Channel,
            organization=organization,
            slug=channel_slug,
            is_active=True
        )

        if not channel.can_user_access(self.request.user):
            raise InvalidRequestException({"message": "You don't have access to this channel."})

        membership = get_object_or_404(
            ChannelMembership,
            channel=channel,
            user_id=user_id,
            is_active=True
        )
        return channel, membership

    @extend_schema(
        request=ChannelMembershipUpdateSerializerIn,
        responses={status.HTTP_200_OK: ChannelMembershipSerializerOut}
    )
    def patch(self, request, org_slug, channel_slug, user_id):
        """Update member role or settings."""
        channel, membership = self.get_objects(org_slug, channel_slug, user_id)

        # Check permissions for role updates
        if 'role' in request.data:
            if not channel.is_admin(request.user):
                raise InvalidRequestException({"message": "You don't have permission to update member roles."})
            if membership.role == 'owner':
                raise InvalidRequestException({"message": "Cannot modify owner role."})

        # Users can update their own notification settings
        if membership.user != request.user and 'mute_notifications' in request.data:
            raise InvalidRequestException({"message": "You can only update your own notification settings."})

        serializer = ChannelMembershipUpdateSerializerIn(
            membership,
            data=request.data,
            partial=True
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        membership = serializer.save()
        response_data = ChannelMembershipSerializerOut(membership).data
        response_data['message'] = 'Member updated successfully'

        return Response(response_data, status=status.HTTP_200_OK)

    @extend_schema(responses={status.HTTP_204_NO_CONTENT})
    def delete(self, request, org_slug, channel_slug, user_id):
        """Remove member from channel."""
        channel, membership = self.get_objects(org_slug, channel_slug, user_id)

        # Check permissions
        if membership.user == request.user:
            # User can leave channel themselves
            if membership.role == 'owner':
                raise InvalidRequestException({"message": "Owner cannot leave channel. Transfer ownership first."})
        else:
            # Admin can remove other members
            if not channel.is_admin(request.user):
                raise InvalidRequestException({"message": "You don't have permission to remove members."})
            if membership.role == 'owner':
                raise InvalidRequestException({"message": "Cannot remove channel owner."})

        membership.is_active = False
        membership.save()

        return Response(
            {'message': 'Member removed successfully'},
            status=status.HTTP_204_NO_CONTENT
        )


class ChannelInviteAPIView(APIView):
    """API view for inviting users to channels."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=ChannelInviteSerializerIn,
        responses={status.HTTP_200_OK}
    )
    def post(self, request, org_slug, channel_slug):
        """Invite users to channel."""
        organization = get_object_or_404(Organization, slug=org_slug, is_active=True)
        channel = get_object_or_404(
            Channel,
            organization=organization,
            slug=channel_slug,
            is_active=True
        )

        if not channel.is_admin(request.user):
            raise InvalidRequestException({"message": "You don't have permission to invite users to this channel."})

        serializer = ChannelInviteSerializerIn(
            data=request.data,
            context={'channel': channel}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        user_ids = serializer.validated_data['user_ids']
        role = serializer.validated_data['role']

        # Create memberships
        users = User.objects.filter(id__in=user_ids)
        created_memberships = []

        for user in users:
            membership = ChannelMembership.objects.create(
                channel=channel,
                user=user,
                role=role,
                is_active=True,
                invited_by=request.user
            )
            created_memberships.append(membership)

        return Response({
            'message': f'Successfully invited {len(created_memberships)} users to the channel',
            'invited_users': [m.user.get_full_name() for m in created_memberships]
        }, status=status.HTTP_200_OK)


class DirectMessageCreateAPIView(APIView):
    """API view for creating direct message channels."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=DirectMessageCreateSerializerIn,
        responses={status.HTTP_201_CREATED: ChannelSerializerOut}
    )
    def post(self, request):
        """Create or get existing direct message channel."""
        serializer = DirectMessageCreateSerializerIn(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        organization = serializer.validated_data['organization']
        target_user = serializer.validated_data['target_user']

        # Check if DM channel already exists
        existing_dm = Channel.objects.filter(
            organization=organization,
            channel_type='direct',
            participants=request.user
        ).filter(participants=target_user).first()

        if existing_dm:
            response_data = ChannelSerializerOut(
                existing_dm,
                context={'request': request}
            ).data
            response_data['message'] = 'Direct message channel already exists'
            return Response(response_data, status=status.HTTP_200_OK)

        # Create new DM channel
        channel = Channel.objects.create(
            name=f"DM: {request.user.get_full_name()} & {target_user.get_full_name()}",
            organization=organization,
            channel_type='direct',
            created_by=request.user,
            allow_threads=True,
            allow_file_uploads=True
        )

        # Add both users as members
        ChannelMembership.objects.create(
            channel=channel,
            user=request.user,
            role='member',
            is_active=True
        )
        ChannelMembership.objects.create(
            channel=channel,
            user=target_user,
            role='member',
            is_active=True
        )

        response_data = ChannelSerializerOut(
            channel,
            context={'request': request}
        ).data
        response_data['message'] = 'Direct message channel created successfully'

        return Response(response_data, status=status.HTTP_201_CREATED)


class JoinChannelAPIView(APIView):
    """API view for joining a public channel."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = JoinChannelSerializerOut

    @extend_schema(responses={
        status.HTTP_200_OK: JoinChannelSerializerOut,
        status.HTTP_400_BAD_REQUEST: serializers.Serializer
    })
    def post(self, request, org_slug, channel_slug):
        """Join a public channel."""
        organization = get_object_or_404(Organization, slug=org_slug, is_active=True)
        channel = get_object_or_404(
            Channel,
            organization=organization,
            slug=channel_slug,
            is_active=True
        )

        if not organization.is_member(request.user):
            return Response(
                {'error': 'You are not a member of this organization'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if channel.channel_type != 'public':
            return Response(
                {'error': 'Can only join public channels'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if channel.is_member(request.user):
            return Response(
                {'error': 'You are already a member of this channel'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if channel.is_at_capacity:
            return Response(
                {'error': 'Channel is at maximum capacity'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create membership
        ChannelMembership.objects.create(
            channel=channel,
            user=request.user,
            role='member',
            is_active=True
        )

        return Response(
            {'message': 'Joined channel successfully'},
            status=status.HTTP_200_OK
        )


class LeaveChannelAPIView(APIView):
    """API view for leaving a channel."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = LeaveChannelSerializerOut

    @extend_schema(responses={
        status.HTTP_200_OK: LeaveChannelSerializerOut,
        status.HTTP_400_BAD_REQUEST: serializers.Serializer
    })
    def post(self, request, org_slug, channel_slug):
        """Leave a channel."""
        organization = get_object_or_404(Organization, slug=org_slug, is_active=True)
        channel = get_object_or_404(
            Channel,
            organization=organization,
            slug=channel_slug,
            is_active=True
        )

        try:
            membership = channel.memberships.get(user=request.user, is_active=True)
        except ChannelMembership.DoesNotExist:
            return Response(
                {'error': 'You are not a member of this channel'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if membership.role == 'owner':
            return Response(
                {'error': 'Owner cannot leave channel. Transfer ownership first.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if channel.is_default:
            return Response(
                {'error': 'Cannot leave default channel'},
                status=status.HTTP_400_BAD_REQUEST
            )

        membership.is_active = False
        membership.save()

        return Response(
            {'message': 'Left channel successfully'},
            status=status.HTTP_200_OK
        )
