# Generated by Django 5.2.5 on 2025-08-08 08:42

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('organizations', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Channel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Channel name', max_length=255)),
                ('slug', models.SlugField(help_text='URL-friendly channel identifier', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Channel description', null=True)),
                ('channel_type', models.CharField(choices=[('public', 'Public'), ('private', 'Private'), ('direct', 'Direct Message')], default='public', help_text='Channel type (public, private, direct)', max_length=20)),
                ('is_active', models.BooleanField(default=True, help_text='Whether channel is active')),
                ('is_archived', models.BooleanField(default=False, help_text='Whether channel is archived')),
                ('is_default', models.BooleanField(default=False, help_text='Whether this is a default channel for the organization')),
                ('allow_threads', models.BooleanField(default=True, help_text='Allow threaded replies in this channel')),
                ('allow_file_uploads', models.BooleanField(default=True, help_text='Allow file uploads in this channel')),
                ('max_members', models.PositiveIntegerField(blank=True, help_text='Maximum number of members (null for unlimited)', null=True)),
                ('created_by', models.ForeignKey(help_text='User who created this channel', on_delete=django.db.models.deletion.CASCADE, related_name='created_channels', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(help_text='Organization this channel belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='channels', to='organizations.organization')),
            ],
            options={
                'verbose_name': 'Channel',
                'verbose_name_plural': 'Channels',
                'db_table': 'channels_channel',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ChannelMembership',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('role', models.CharField(choices=[('owner', 'Owner'), ('admin', 'Admin'), ('member', 'Member')], default='member', help_text="User's role in the channel", max_length=20)),
                ('is_active', models.BooleanField(default=True, help_text='Whether membership is active')),
                ('joined_at', models.DateTimeField(auto_now_add=True, help_text='When user joined the channel')),
                ('mute_notifications', models.BooleanField(default=False, help_text='Whether notifications are muted for this channel')),
                ('last_read_at', models.DateTimeField(blank=True, help_text='Last time user read messages in this channel', null=True)),
                ('channel', models.ForeignKey(help_text='Channel', on_delete=django.db.models.deletion.CASCADE, related_name='memberships', to='channels_app.channel')),
                ('invited_by', models.ForeignKey(blank=True, help_text='User who invited this member', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_channel_invitations', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(help_text='User', on_delete=django.db.models.deletion.CASCADE, related_name='channel_memberships', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Channel Membership',
                'verbose_name_plural': 'Channel Memberships',
                'db_table': 'channels_membership',
                'ordering': ['-joined_at'],
                'unique_together': {('channel', 'user')},
            },
        ),
        migrations.AddField(
            model_name='channel',
            name='participants',
            field=models.ManyToManyField(help_text='Channel participants', related_name='channels', through='channels_app.ChannelMembership', through_fields=('channel', 'user'), to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='channel',
            unique_together={('organization', 'slug')},
        ),
    ]
