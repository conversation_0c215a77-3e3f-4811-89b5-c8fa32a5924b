from rest_framework import serializers
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema_field

from mycorrmessaging.modules.choices import CHANNEL_TYPE_CHOICES
from mycorrmessaging.modules.exceptions import InvalidRequestException
from organizations.models import Organization
from .models import Channel, ChannelMembership

User = get_user_model()


class ChannelCreateSerializerIn(serializers.Serializer):
    """Serializer for creating channels."""
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    name = serializers.CharField(max_length=255, help_text="Channel name")
    description = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Channel description"
    )
    channel_type = serializers.ChoiceField(
        choices=CHANNEL_TYPE_CHOICES,
        default='public',
        help_text="Channel type (public, private, direct)"
    )
    participants = serializers.ListField(
        child=serializers.UUIDField(),
        help_text="List of user IDs to add as participants"
    )
    parent_channel = serializers.UUIDField(
        required=False,
        help_text="Parent channel for group channels"
    )

    def create(self, validated_data):
        user = validated_data.get('user')
        name = validated_data.get('name')
        description = validated_data.get('description')
        channel_type = validated_data.get('channel_type')
        participants = validated_data.get('participants')
        parent_channel_id = validated_data.get('parent_channel')

        organization = Organization.objects.filter(
            memberships__user=user,
            memberships__status='active'
        ).first()
        if not organization:
            raise InvalidRequestException({"message": "You are not a member of any organization."})

        if channel_type == 'public' and not organization.allow_public_channels:
            raise InvalidRequestException({"message": "Public channels are not allowed in this organization."})

        # Validate that all participants are organization members
        for participant_id in participants:
            if not organization.is_member(User.objects.get(id=participant_id)):
                raise InvalidRequestException({"message": f"User with ID {participant_id} is not a member of this organization."})

        if parent_channel_id:
            try:
                parent_channel = Channel.objects.get(id=parent_channel_id, is_active=True)
                if parent_channel.organization != organization:
                    raise InvalidRequestException({"message": "Parent channel must be in the same organization."})
            except Channel.DoesNotExist:
                raise InvalidRequestException({"message": "Parent channel not found."})
        else:
            parent_channel = None

        # Create channel
        channel = Channel.objects.create(
            name=name,
            description=description,
            channel_type=channel_type,
            organization=organization,
            created_by=user,
            parent_channel=parent_channel
        )

        # Add participants
        for participant_id in participants:
            ChannelMembership.objects.create(
                channel=channel,
                user=User.objects.get(id=participant_id),
                role='member',
                is_active=True,
                invited_by=user
            )

        return channel


class ChannelUpdateSerializerIn(serializers.ModelSerializer):
    """Serializer for updating channels."""
    class Meta:
        model = Channel
        fields = [
            'name', 'description', 'allow_threads', 'allow_file_uploads',
            'max_members', 'is_archived'
        ]


class ChannelSerializerOut(serializers.ModelSerializer):
    """Serializer for channel output."""
    member_count = serializers.ReadOnlyField()
    is_at_capacity = serializers.ReadOnlyField()
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    user_role = serializers.SerializerMethodField()
    is_member = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()
    group_chats = serializers.SerializerMethodField()

    def get_group_chats(self, obj):
        return ChannelSerializerOut(obj.group_channels.all(), many=True).data
    
    class Meta:
        model = Channel
        fields = [
            'id', 'name', 'slug', 'description', 'organization', 'organization_name',
            'channel_type', 'is_active', 'is_archived', 'is_default',
            'allow_threads', 'allow_file_uploads', 'max_members', 'group_chats',
            'member_count', 'is_at_capacity', 'created_by', 'created_by_name',
            'user_role', 'is_member', 'unread_count', 'created_at', 'updated_at'
        ]
    
    @extend_schema_field(serializers.CharField)
    def get_user_role(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.get_member_role(request.user)
        return None

    @extend_schema_field(serializers.BooleanField)
    def get_is_member(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.is_member(request.user)
        return False

    @extend_schema_field(serializers.IntegerField)
    def get_unread_count(self, obj):
        # TODO: Implement unread message count
        return 0


class ChannelListSerializerOut(serializers.ModelSerializer):
    """Serializer for channel list output."""
    member_count = serializers.ReadOnlyField()
    user_role = serializers.SerializerMethodField()
    is_member = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()
    last_message_at = serializers.SerializerMethodField()
    
    class Meta:
        model = Channel
        fields = [
            'id', 'name', 'slug', 'description', 'channel_type',
            'is_archived', 'is_default', 'member_count',
            'user_role', 'is_member', 'unread_count', 'last_message_at'
        ]
    
    @extend_schema_field(serializers.CharField)
    def get_user_role(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.get_member_role(request.user)
        return None

    @extend_schema_field(serializers.BooleanField)
    def get_is_member(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.is_member(request.user)
        return False

    @extend_schema_field(serializers.IntegerField)
    def get_unread_count(self, obj):
        # TODO: Implement unread message count
        return 0

    @extend_schema_field(serializers.DateTimeField)
    def get_last_message_at(self, obj):
        # TODO: Implement last message timestamp
        return None


class ChannelMembershipSerializerOut(serializers.ModelSerializer):
    """Serializer for channel membership output."""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_avatar = serializers.CharField(source='user.avatar', read_only=True)
    user_status = serializers.CharField(source='user.status', read_only=True)
    invited_by_name = serializers.CharField(source='invited_by.get_full_name', read_only=True)
    
    class Meta:
        model = ChannelMembership
        fields = [
            'id', 'user', 'user_name', 'user_email', 'user_avatar', 'user_status',
            'role', 'is_active', 'joined_at', 'invited_by', 'invited_by_name',
            'mute_notifications', 'last_read_at'
        ]


class ChannelMembershipUpdateSerializerIn(serializers.ModelSerializer):
    """Serializer for updating channel membership."""
    class Meta:
        model = ChannelMembership
        fields = ['role', 'mute_notifications']


class ChannelInviteSerializerIn(serializers.Serializer):
    """Serializer for inviting users to channels."""
    user_ids = serializers.ListField(
        child=serializers.UUIDField(),
        help_text="List of user IDs to invite"
    )
    role = serializers.ChoiceField(
        choices=[('member', 'Member'), ('admin', 'Admin')],
        default='member'
    )
    
    def validate_user_ids(self, value):
        if not value:
            raise InvalidRequestException({"message": "At least one user ID is required."})
        
        # Check if users exist and are organization members
        channel = self.context['channel']
        users = User.objects.filter(id__in=value)
        
        if len(users) != len(value):
            raise InvalidRequestException({"message": "Some users were not found."})
        
        for user in users:
            if not channel.organization.is_member(user):
                raise InvalidRequestException({"message": f"User {user.get_full_name()} is not a member of the organization."})
            if channel.is_member(user):
                raise InvalidRequestException({"message": f"User {user.get_full_name()} is already a member of this channel."})
        
        return value


class DirectMessageCreateSerializerIn(serializers.Serializer):
    """Serializer for creating direct message channels."""
    user_id = serializers.UUIDField(help_text="ID of the user to start DM with")
    organization_slug = serializers.SlugField(help_text="Organization slug")
    
    def validate(self, attrs):
        user_id = attrs['user_id']
        organization_slug = attrs['organization_slug']
        request_user = self.context['request'].user
        
        # Check if organization exists and user is member
        try:
            organization = Organization.objects.get(slug=organization_slug, is_active=True)
            if not organization.is_member(request_user):
                raise InvalidRequestException({"message": "You are not a member of this organization."})
        except Organization.DoesNotExist:
            raise InvalidRequestException({"message": "Organization not found."})
        
        # Check if target user exists and is organization member
        try:
            target_user = User.objects.get(id=user_id)
            if not organization.is_member(target_user):
                raise InvalidRequestException({"message": "Target user is not a member of this organization."})
        except User.DoesNotExist:
            raise InvalidRequestException({"message": "Target user not found."})

        attrs['organization'] = organization
        attrs['target_user'] = target_user
        return attrs


# Response serializers for function-based views
class JoinChannelSerializerOut(serializers.Serializer):
    """Serializer for join channel response."""
    message = serializers.CharField(help_text="Success message")


class LeaveChannelSerializerOut(serializers.Serializer):
    """Serializer for leave channel response."""
    message = serializers.CharField(help_text="Success message")
