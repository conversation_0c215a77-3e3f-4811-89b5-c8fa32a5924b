from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Channel, ChannelMembership


@receiver(post_save, sender=Channel)
def create_channel_owner_membership(sender, instance, created, **kwargs):
    """
    Create owner membership for the user who created the channel.
    """
    if created and instance.channel_type != 'direct':
        ChannelMembership.objects.create(
            channel=instance,
            user=instance.created_by,
            role='owner',
            is_active=True
        )


# @receiver(post_save, sender=Channel)
# def add_default_members_to_public_channel(sender, instance, created, **kwargs):
#     """
#     Add all organization members to public channels if it's a default channel.
#     """
#     if created and instance.channel_type == 'public' and instance.is_default:
#         # Get all active organization members
#         org_members = instance.organization.memberships.filter(status='active')
#
#         for membership in org_members:
#             # Skip if user is already added (creator)
#             if not instance.is_member(membership.user):
#                 ChannelMembership.objects.create(
#                     channel=instance,
#                     user=membership.user,
#                     role='member',
#                     is_active=True
#                 )
