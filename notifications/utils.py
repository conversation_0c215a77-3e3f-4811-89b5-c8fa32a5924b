from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import Notification, NotificationPreference

User = get_user_model()


def create_notification(
    recipient, 
    notification_type, 
    title, 
    content, 
    sender=None, 
    organization=None, 
    channel=None, 
    related_message=None, 
    data=None
):
    """
    Create a notification for a user.
    """
    notification = Notification.objects.create(
        recipient=recipient,
        sender=sender,
        notification_type=notification_type,
        title=title,
        content=content,
        organization=organization,
        channel=channel,
        related_message=related_message,
        data=data or {}
    )
    
    # Send real-time notification via WebSocket
    try:
        from messaging.utils import send_notification_to_user
        send_notification_to_user(str(recipient.id), {
            'notification_id': str(notification.id),
            'type': notification_type,
            'title': title,
            'content': content,
            'sender_name': sender.get_full_name() if sender else None,
            'timestamp': notification.created_at.isoformat()
        })
    except Exception:
        pass
    
    return notification


def create_mention_notification(message, mentioned_user):
    """
    Create a mention notification.
    """
    if mentioned_user == message.sender:
        return None  # Don't notify users about their own mentions
    
    title = f"You were mentioned by {message.sender.get_full_name()}"
    content = f"In #{message.channel.name}: {message.get_content()[:100]}..."
    
    return create_notification(
        recipient=mentioned_user,
        notification_type='mention',
        title=title,
        content=content,
        sender=message.sender,
        organization=message.channel.organization,
        channel=message.channel,
        related_message=message,
        data={
            'message_id': str(message.id),
            'channel_id': str(message.channel.id),
            'channel_name': message.channel.name
        }
    )


def create_direct_message_notification(message, recipient):
    """
    Create a direct message notification.
    """
    if recipient == message.sender:
        return None  # Don't notify sender
    
    title = f"New message from {message.sender.get_full_name()}"
    content = message.get_content()[:100] + "..." if len(message.get_content()) > 100 else message.get_content()
    
    return create_notification(
        recipient=recipient,
        notification_type='direct_message',
        title=title,
        content=content,
        sender=message.sender,
        organization=message.channel.organization,
        channel=message.channel,
        related_message=message,
        data={
            'message_id': str(message.id),
            'channel_id': str(message.channel.id)
        }
    )


def create_channel_invite_notification(channel, invited_user, inviter):
    """
    Create a channel invitation notification.
    """
    title = f"You've been invited to #{channel.name}"
    content = f"{inviter.get_full_name()} invited you to join the {channel.name} channel"
    
    return create_notification(
        recipient=invited_user,
        notification_type='channel_invite',
        title=title,
        content=content,
        sender=inviter,
        organization=channel.organization,
        channel=channel,
        data={
            'channel_id': str(channel.id),
            'channel_name': channel.name,
            'inviter_id': str(inviter.id)
        }
    )


def create_organization_invite_notification(organization, invited_email, inviter):
    """
    Create an organization invitation notification.
    """
    try:
        invited_user = User.objects.get(email=invited_email)
        
        title = f"You've been invited to {organization.name}"
        content = f"{inviter.get_full_name()} invited you to join {organization.name}"
        
        return create_notification(
            recipient=invited_user,
            notification_type='organization_invite',
            title=title,
            content=content,
            sender=inviter,
            organization=organization,
            data={
                'organization_id': str(organization.id),
                'organization_name': organization.name,
                'inviter_id': str(inviter.id)
            }
        )
    except User.DoesNotExist:
        return None


def should_send_notification(user, notification_type):
    """
    Check if user should receive a notification based on their preferences.
    """
    try:
        prefs = user.notification_preferences
        
        # Check quiet hours
        if prefs.is_quiet_hours():
            return False
        
        # Check notification type preferences
        if notification_type == 'mention':
            return prefs.push_mentions or prefs.desktop_mentions
        elif notification_type == 'direct_message':
            return prefs.push_direct_messages or prefs.desktop_direct_messages
        elif notification_type == 'channel_message':
            return prefs.push_channel_messages or prefs.desktop_channel_messages
        
        return True
    except NotificationPreference.DoesNotExist:
        return True


def mark_notifications_as_read(user, notification_ids=None, notification_type=None):
    """
    Mark notifications as read for a user.
    """
    queryset = Notification.objects.filter(recipient=user, status='unread')
    
    if notification_ids:
        queryset = queryset.filter(id__in=notification_ids)
    
    if notification_type:
        queryset = queryset.filter(notification_type=notification_type)
    
    count = queryset.update(
        status='read',
        read_at=timezone.now()
    )
    
    return count


def get_unread_notification_count(user):
    """
    Get count of unread notifications for a user.
    """
    return Notification.objects.filter(
        recipient=user,
        status='unread'
    ).count()
