from rest_framework import serializers
from django.contrib.auth import get_user_model

from mycorrmessaging.modules.exceptions import InvalidRequestException
from .models import Notification, NotificationPreference

User = get_user_model()


class NotificationSerializerOut(serializers.ModelSerializer):
    """Serializer for notification output."""
    sender_name = serializers.CharField(source='sender.get_full_name', read_only=True)
    sender_avatar = serializers.CharField(source='sender.avatar', read_only=True)
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    channel_name = serializers.Char<PERSON>ield(source='channel.name', read_only=True)
    is_read = serializers.ReadOnlyField()
    
    class Meta:
        model = Notification
        fields = [
            'id', 'notification_type', 'title', 'content', 'status',
            'sender', 'sender_name', 'sender_avatar', 'organization',
            'organization_name', 'channel', 'channel_name', 'related_message',
            'data', 'is_read', 'read_at', 'dismissed_at', 'created_at'
        ]


class NotificationListSerializerOut(serializers.ModelSerializer):
    """Serializer for notification list output (lighter version)."""
    sender_name = serializers.CharField(source='sender.get_full_name', read_only=True)
    is_read = serializers.ReadOnlyField()
    
    class Meta:
        model = Notification
        fields = [
            'id', 'notification_type', 'title', 'status', 'sender_name',
            'is_read', 'created_at'
        ]


class NotificationUpdateSerializerIn(serializers.ModelSerializer):
    """Serializer for updating notification status."""
    class Meta:
        model = Notification
        fields = ['status']


class NotificationPreferencesSerializerIn(serializers.ModelSerializer):
    """Serializer for notification preferences input."""
    class Meta:
        model = NotificationPreference
        fields = [
            'email_mentions', 'email_direct_messages', 'email_channel_messages',
            'email_invitations', 'push_mentions', 'push_direct_messages',
            'push_channel_messages', 'desktop_mentions', 'desktop_direct_messages',
            'desktop_channel_messages', 'quiet_hours_enabled', 'quiet_hours_start',
            'quiet_hours_end'
        ]


class NotificationPreferencesSerializerOut(serializers.ModelSerializer):
    """Serializer for notification preferences output."""
    class Meta:
        model = NotificationPreference
        fields = [
            'id', 'email_mentions', 'email_direct_messages', 'email_channel_messages',
            'email_invitations', 'push_mentions', 'push_direct_messages',
            'push_channel_messages', 'desktop_mentions', 'desktop_direct_messages',
            'desktop_channel_messages', 'quiet_hours_enabled', 'quiet_hours_start',
            'quiet_hours_end', 'created_at', 'updated_at'
        ]


class BulkNotificationActionSerializerIn(serializers.Serializer):
    """Serializer for bulk notification actions."""
    notification_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        help_text="List of notification IDs to act on"
    )
    action = serializers.ChoiceField(
        choices=[('read', 'Mark as Read'), ('dismissed', 'Dismiss')],
        help_text="Action to perform"
    )
    notification_type = serializers.CharField(
        required=False,
        help_text="Filter by notification type"
    )
    
    def validate(self, attrs):
        if not attrs.get('notification_ids') and not attrs.get('notification_type'):
            raise InvalidRequestException({"message": "Either notification_ids or notification_type must be provided"})
        return attrs


# Response serializers for function-based views
class NotificationStatsSerializerOut(serializers.Serializer):
    """Serializer for notification statistics response."""
    total_unread = serializers.IntegerField(help_text="Total unread notifications")
    unread_by_type = serializers.DictField(help_text="Unread notifications by type")
    total_notifications = serializers.IntegerField(help_text="Total notifications")


class MarkAllAsReadSerializerOut(serializers.Serializer):
    """Serializer for mark all as read response."""
    message = serializers.CharField(help_text="Success message")
    count = serializers.IntegerField(help_text="Number of notifications marked as read")
