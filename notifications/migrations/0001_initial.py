# Generated by Django 5.2.5 on 2025-08-08 09:24

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('channels_app', '0001_initial'),
        ('messaging', '0002_messageattachment'),
        ('organizations', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('email_mentions', models.BooleanField(default=True, help_text='Email notifications for mentions')),
                ('email_direct_messages', models.BooleanField(default=True, help_text='Email notifications for direct messages')),
                ('email_channel_messages', models.Bo<PERSON>anField(default=False, help_text='Email notifications for channel messages')),
                ('email_invitations', models.BooleanField(default=True, help_text='Email notifications for invitations')),
                ('push_mentions', models.BooleanField(default=True, help_text='Push notifications for mentions')),
                ('push_direct_messages', models.BooleanField(default=True, help_text='Push notifications for direct messages')),
                ('push_channel_messages', models.BooleanField(default=False, help_text='Push notifications for channel messages')),
                ('desktop_mentions', models.BooleanField(default=True, help_text='Desktop notifications for mentions')),
                ('desktop_direct_messages', models.BooleanField(default=True, help_text='Desktop notifications for direct messages')),
                ('desktop_channel_messages', models.BooleanField(default=False, help_text='Desktop notifications for channel messages')),
                ('quiet_hours_enabled', models.BooleanField(default=False, help_text='Enable quiet hours')),
                ('quiet_hours_start', models.TimeField(blank=True, help_text='Quiet hours start time', null=True)),
                ('quiet_hours_end', models.TimeField(blank=True, help_text='Quiet hours end time', null=True)),
                ('user', models.OneToOneField(help_text='User', on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification Preference',
                'verbose_name_plural': 'Notification Preferences',
                'db_table': 'notifications_preference',
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('notification_type', models.CharField(choices=[('mention', 'Mention'), ('direct_message', 'Direct Message'), ('channel_message', 'Channel Message'), ('channel_invite', 'Channel Invite'), ('organization_invite', 'Organization Invite'), ('system', 'System')], help_text='Type of notification', max_length=30)),
                ('title', models.CharField(help_text='Notification title', max_length=255)),
                ('content', models.TextField(help_text='Notification content')),
                ('status', models.CharField(choices=[('unread', 'Unread'), ('read', 'Read'), ('dismissed', 'Dismissed')], default='unread', help_text='Notification status', max_length=20)),
                ('data', models.JSONField(blank=True, default=dict, help_text='Additional notification data')),
                ('read_at', models.DateTimeField(blank=True, help_text='When notification was read', null=True)),
                ('dismissed_at', models.DateTimeField(blank=True, help_text='When notification was dismissed', null=True)),
                ('channel', models.ForeignKey(blank=True, help_text='Related channel', null=True, on_delete=django.db.models.deletion.CASCADE, to='channels_app.channel')),
                ('organization', models.ForeignKey(blank=True, help_text='Related organization', null=True, on_delete=django.db.models.deletion.CASCADE, to='organizations.organization')),
                ('recipient', models.ForeignKey(help_text='User receiving the notification', on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
                ('related_message', models.ForeignKey(blank=True, help_text='Related message', null=True, on_delete=django.db.models.deletion.CASCADE, to='messaging.message')),
                ('sender', models.ForeignKey(blank=True, help_text='User who triggered the notification', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sent_notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'db_table': 'notifications_notification',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['recipient', '-created_at'], name='notificatio_recipie_a972ce_idx'), models.Index(fields=['recipient', 'status'], name='notificatio_recipie_e285de_idx'), models.Index(fields=['notification_type', '-created_at'], name='notificatio_notific_d8746a_idx')],
            },
        ),
    ]
