from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.utils import timezone
from drf_spectacular.utils import extend_schema
from mycorrmessaging.modules.exceptions import raise_serializer_error_msg
from mycorrmessaging.modules.paginations import CustomPagination
from .models import Notification, NotificationPreference
from .serializers import (
    NotificationSerializerOut, NotificationListSerializerOut,
    NotificationUpdateSerializerIn, NotificationPreferencesSerializerIn,
    NotificationPreferencesSerializerOut, BulkNotificationActionSerializerIn,
    NotificationStatsSerializerOut, MarkAllAsReadSerializerOut
)
from .utils import mark_notifications_as_read, get_unread_notification_count


class NotificationListAPIView(generics.ListAPIView):
    """API view for listing user notifications."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = NotificationListSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        user = self.request.user
        queryset = Notification.objects.filter(recipient=user)

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by type
        type_filter = self.request.query_params.get('type')
        if type_filter:
            queryset = queryset.filter(notification_type=type_filter)

        return queryset.select_related('sender', 'organization', 'channel')


class NotificationDetailAPIView(APIView):
    """API view for notification details."""
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self, notification_id):
        notification = get_object_or_404(
            Notification,
            id=notification_id,
            recipient=self.request.user
        )
        return notification

    @extend_schema(responses={status.HTTP_200_OK: NotificationSerializerOut})
    def get(self, request, notification_id):
        """Get notification details."""
        notification = self.get_object(notification_id)

        # Mark as read when viewed
        if notification.status == 'unread':
            notification.mark_as_read()

        serializer = NotificationSerializerOut(notification)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        request=NotificationUpdateSerializerIn,
        responses={status.HTTP_200_OK: NotificationSerializerOut}
    )
    def patch(self, request, notification_id):
        """Update notification status."""
        notification = self.get_object(notification_id)

        serializer = NotificationUpdateSerializerIn(
            notification,
            data=request.data,
            partial=True
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        new_status = serializer.validated_data.get('status')
        if new_status == 'read':
            notification.mark_as_read()
        elif new_status == 'dismissed':
            notification.mark_as_dismissed()

        response_data = NotificationSerializerOut(notification).data
        response_data['message'] = 'Notification updated successfully'

        return Response(response_data, status=status.HTTP_200_OK)

    @extend_schema(responses={status.HTTP_204_NO_CONTENT})
    def delete(self, request, notification_id):
        """Delete notification."""
        notification = self.get_object(notification_id)
        notification.delete()

        return Response(
            {'message': 'Notification deleted successfully'},
            status=status.HTTP_204_NO_CONTENT
        )


class NotificationPreferencesAPIView(APIView):
    """API view for notification preferences."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: NotificationPreferencesSerializerOut})
    def get(self, request):
        """Get user's notification preferences."""
        preferences, created = NotificationPreference.objects.get_or_create(
            user=request.user
        )
        serializer = NotificationPreferencesSerializerOut(preferences)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        request=NotificationPreferencesSerializerIn,
        responses={status.HTTP_200_OK: NotificationPreferencesSerializerOut}
    )
    def patch(self, request):
        """Update user's notification preferences."""
        preferences, created = NotificationPreference.objects.get_or_create(
            user=request.user
        )

        serializer = NotificationPreferencesSerializerIn(
            preferences,
            data=request.data,
            partial=True
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        preferences = serializer.save()
        response_data = NotificationPreferencesSerializerOut(preferences).data
        response_data['message'] = 'Preferences updated successfully'

        return Response(response_data, status=status.HTTP_200_OK)


class BulkNotificationActionAPIView(APIView):
    """API view for bulk notification actions."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=BulkNotificationActionSerializerIn,
        responses={status.HTTP_200_OK}
    )
    def post(self, request):
        """Perform bulk actions on notifications."""
        serializer = BulkNotificationActionSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        notification_ids = serializer.validated_data.get('notification_ids')
        action = serializer.validated_data['action']
        notification_type = serializer.validated_data.get('notification_type')

        # Build queryset
        queryset = Notification.objects.filter(recipient=request.user)

        if notification_ids:
            queryset = queryset.filter(id__in=notification_ids)

        if notification_type:
            queryset = queryset.filter(notification_type=notification_type)

        # Perform action
        if action == 'read':
            count = queryset.filter(status='unread').update(
                status='read',
                read_at=timezone.now()
            )
            message = f'Marked {count} notifications as read'
        elif action == 'dismissed':
            count = queryset.filter(status__in=['unread', 'read']).update(
                status='dismissed',
                dismissed_at=timezone.now()
            )
            message = f'Dismissed {count} notifications'

        return Response({'message': message, 'count': count}, status=status.HTTP_200_OK)


class NotificationStatsAPIView(APIView):
    """API view for getting notification statistics for the user."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: NotificationStatsSerializerOut})
    def get(self, request):
        """Get notification statistics for the user."""
        user = request.user

        stats = {
            'total_unread': Notification.objects.filter(
                recipient=user, status='unread'
            ).count(),
            'unread_by_type': {},
            'total_notifications': Notification.objects.filter(recipient=user).count()
        }

        # Get unread count by type
        from mycorrmessaging.modules.choices import NOTIFICATION_TYPE_CHOICES
        for type_code, type_name in NOTIFICATION_TYPE_CHOICES:
            count = Notification.objects.filter(
                recipient=user,
                status='unread',
                notification_type=type_code
            ).count()
            if count > 0:
                stats['unread_by_type'][type_code] = count

        return Response(stats, status=status.HTTP_200_OK)


class MarkAllAsReadAPIView(APIView):
    """API view for marking all notifications as read for the user."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: MarkAllAsReadSerializerOut})
    def post(self, request):
        """Mark all notifications as read for the user."""
        count = mark_notifications_as_read(request.user)

        return Response({
            'message': f'Marked {count} notifications as read',
            'count': count
        }, status=status.HTTP_200_OK)
