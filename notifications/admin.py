from django.contrib import admin
from django.utils.html import format_html
from .models import Notification, NotificationPreference


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Admin configuration for Notification model."""

    list_display = [
        'title', 'recipient', 'notification_type', 'status',
        'sender', 'is_read', 'created_at'
    ]
    list_filter = [
        'notification_type', 'status', 'created_at', 'read_at'
    ]
    search_fields = [
        'title', 'content', 'recipient__email', 'recipient__first_name',
        'recipient__last_name', 'sender__email'
    ]
    readonly_fields = [
        'id', 'is_read', 'created_at', 'updated_at', 'read_at', 'dismissed_at'
    ]

    fieldsets = (
        (None, {
            'fields': ('recipient', 'sender', 'notification_type', 'title', 'content')
        }),
        ('Status', {
            'fields': ('status', 'is_read', 'read_at', 'dismissed_at')
        }),
        ('Related Objects', {
            'fields': ('organization', 'channel', 'related_message')
        }),
        ('Data', {
            'fields': ('data',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'recipient', 'sender', 'organization', 'channel'
        )


@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    """Admin configuration for NotificationPreference model."""

    list_display = [
        'user', 'email_mentions', 'push_mentions', 'desktop_mentions',
        'quiet_hours_enabled', 'created_at'
    ]
    list_filter = [
        'email_mentions', 'push_mentions', 'desktop_mentions',
        'quiet_hours_enabled', 'created_at'
    ]
    search_fields = ['user__email', 'user__first_name', 'user__last_name']
    readonly_fields = ['id', 'created_at', 'updated_at']

    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Email Notifications', {
            'fields': (
                'email_mentions', 'email_direct_messages',
                'email_channel_messages', 'email_invitations'
            )
        }),
        ('Push Notifications', {
            'fields': (
                'push_mentions', 'push_direct_messages', 'push_channel_messages'
            )
        }),
        ('Desktop Notifications', {
            'fields': (
                'desktop_mentions', 'desktop_direct_messages', 'desktop_channel_messages'
            )
        }),
        ('Quiet Hours', {
            'fields': ('quiet_hours_enabled', 'quiet_hours_start', 'quiet_hours_end')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')
