from django.db import models
from django.conf import settings
from django.utils import timezone
from home.models import BaseModel
from organizations.models import Organization
from channels_app.models import Channel
from messaging.models import Message
from mycorrmessaging.modules.choices import (
    NOTIFICATION_TYPE_CHOICES, NOTIFICATION_STATUS_CHOICES
)


class Notification(BaseModel):
    """
    Model representing notifications for users.
    """
    recipient = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='notifications',
        help_text="User receiving the notification"
    )
    sender = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='sent_notifications',
        help_text="User who triggered the notification"
    )
    notification_type = models.CharField(
        max_length=30,
        choices=NOTIFICATION_TYPE_CHOICES,
        help_text="Type of notification"
    )
    title = models.CharField(
        max_length=255,
        help_text="Notification title"
    )
    content = models.TextField(
        help_text="Notification content"
    )
    status = models.Char<PERSON>ield(
        max_length=20,
        choices=NOTIFICATION_STATUS_CHOICES,
        default='unread',
        help_text="Notification status"
    )

    # Related objects
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Related organization"
    )
    channel = models.ForeignKey(
        Channel,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Related channel"
    )
    related_message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Related message"
    )

    # Metadata
    data = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional notification data"
    )

    # Timestamps
    read_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When notification was read"
    )
    dismissed_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When notification was dismissed"
    )

    class Meta:
        db_table = 'notifications_notification'
        verbose_name = 'Notification'
        verbose_name_plural = 'Notifications'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', '-created_at']),
            models.Index(fields=['recipient', 'status']),
            models.Index(fields=['notification_type', '-created_at']),
        ]

    def __str__(self):
        return f"{self.title} - {self.recipient.get_full_name()}"

    def mark_as_read(self):
        """Mark notification as read."""
        if self.status == 'unread':
            self.status = 'read'
            self.read_at = timezone.now()
            self.save(update_fields=['status', 'read_at'])

    def mark_as_dismissed(self):
        """Mark notification as dismissed."""
        self.status = 'dismissed'
        self.dismissed_at = timezone.now()
        self.save(update_fields=['status', 'dismissed_at'])

    @property
    def is_read(self):
        """Check if notification is read."""
        return self.status in ['read', 'dismissed']


class NotificationPreference(BaseModel):
    """
    Model for user notification preferences.
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='notification_preferences',
        help_text="User"
    )

    # Email notifications
    email_mentions = models.BooleanField(
        default=True,
        help_text="Email notifications for mentions"
    )
    email_direct_messages = models.BooleanField(
        default=True,
        help_text="Email notifications for direct messages"
    )
    email_channel_messages = models.BooleanField(
        default=False,
        help_text="Email notifications for channel messages"
    )
    email_invitations = models.BooleanField(
        default=True,
        help_text="Email notifications for invitations"
    )

    # Push notifications
    push_mentions = models.BooleanField(
        default=True,
        help_text="Push notifications for mentions"
    )
    push_direct_messages = models.BooleanField(
        default=True,
        help_text="Push notifications for direct messages"
    )
    push_channel_messages = models.BooleanField(
        default=False,
        help_text="Push notifications for channel messages"
    )

    # Desktop notifications
    desktop_mentions = models.BooleanField(
        default=True,
        help_text="Desktop notifications for mentions"
    )
    desktop_direct_messages = models.BooleanField(
        default=True,
        help_text="Desktop notifications for direct messages"
    )
    desktop_channel_messages = models.BooleanField(
        default=False,
        help_text="Desktop notifications for channel messages"
    )

    # Quiet hours
    quiet_hours_enabled = models.BooleanField(
        default=False,
        help_text="Enable quiet hours"
    )
    quiet_hours_start = models.TimeField(
        null=True,
        blank=True,
        help_text="Quiet hours start time"
    )
    quiet_hours_end = models.TimeField(
        null=True,
        blank=True,
        help_text="Quiet hours end time"
    )

    class Meta:
        db_table = 'notifications_preference'
        verbose_name = 'Notification Preference'
        verbose_name_plural = 'Notification Preferences'

    def __str__(self):
        return f"{self.user.get_full_name()} - Notification Preferences"

    def is_quiet_hours(self):
        """Check if current time is within quiet hours."""
        if not self.quiet_hours_enabled or not self.quiet_hours_start or not self.quiet_hours_end:
            return False

        from datetime import time
        current_time = timezone.now().time()

        if self.quiet_hours_start <= self.quiet_hours_end:
            # Same day quiet hours
            return self.quiet_hours_start <= current_time <= self.quiet_hours_end
        else:
            # Overnight quiet hours
            return current_time >= self.quiet_hours_start or current_time <= self.quiet_hours_end
