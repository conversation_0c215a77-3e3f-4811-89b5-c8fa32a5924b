from django.urls import path
from . import views

app_name = 'notifications'

urlpatterns = [
    # Notification management
    path('', views.NotificationListAPIView.as_view(), name='list'),
    path('<uuid:notification_id>/', views.NotificationDetailAPIView.as_view(), name='detail'),
    path('bulk-action/', views.BulkNotificationActionAPIView.as_view(), name='bulk_action'),
    path('mark-all-read/', views.MarkAllAsReadAPIView.as_view(), name='mark_all_read'),
    path('stats/', views.NotificationStatsAPIView.as_view(), name='stats'),
    
    # Notification preferences
    path('preferences/', views.NotificationPreferencesAPIView.as_view(), name='preferences'),
]
