from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from organizations.models import Organization, OrganizationMembership
from channels_app.models import Channel, ChannelMembership
from .models import Message, MessageReaction, MessageStatus

User = get_user_model()


class MessageModelTest(TestCase):
    """Test cases for Message model."""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        self.organization = Organization.objects.create(
            name='Test Org',
            slug='test-org',
            created_by=self.user
        )
        self.channel = Channel.objects.create(
            name='test-channel',
            organization=self.organization,
            created_by=self.user
        )

    def test_create_message(self):
        """Test creating a message."""
        message = Message.objects.create(
            channel=self.channel,
            sender=self.user,
            message_type='text'
        )
        message.set_content('Test message content')

        self.assertEqual(message.channel, self.channel)
        self.assertEqual(message.sender, self.user)
        self.assertEqual(message.get_content(), 'Test message content')
        self.assertFalse(message.is_edited)
        self.assertFalse(message.is_deleted)

    def test_message_encryption(self):
        """Test message content encryption."""
        message = Message.objects.create(
            channel=self.channel,
            sender=self.user,
            message_type='text'
        )
        content = 'This is a secret message'
        message.set_content(content)

        # Content should be encrypted in database
        self.assertNotEqual(message.content_encrypted, content)
        # But should decrypt correctly
        self.assertEqual(message.get_content(), content)

    def test_message_soft_delete(self):
        """Test message soft deletion."""
        message = Message.objects.create(
            channel=self.channel,
            sender=self.user,
            message_type='text'
        )
        message.set_content('Test message')

        message.soft_delete()
        self.assertTrue(message.is_deleted)
        self.assertIsNotNone(message.deleted_at)

    def test_message_edit(self):
        """Test message editing."""
        message = Message.objects.create(
            channel=self.channel,
            sender=self.user,
            message_type='text'
        )
        message.set_content('Original content')

        message.mark_as_edited()
        self.assertTrue(message.is_edited)
        self.assertIsNotNone(message.edited_at)


class MessageAPITest(APITestCase):
    """Test cases for Message API endpoints."""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            email='<EMAIL>',
            username='otheruser',
            first_name='Other',
            last_name='User',
            password='otherpass123'
        )
        self.organization = Organization.objects.create(
            name='Test Org',
            slug='test-org',
            created_by=self.user
        )
        # Add other user to organization
        OrganizationMembership.objects.create(
            organization=self.organization,
            user=self.other_user,
            role='member'
        )

        self.channel = Channel.objects.create(
            name='test-channel',
            organization=self.organization,
            created_by=self.user
        )
        # Add other user to channel
        ChannelMembership.objects.create(
            channel=self.channel,
            user=self.other_user,
            role='member'
        )

    def test_send_message(self):
        """Test sending a message."""
        self.client.force_authenticate(user=self.user)
        url = reverse('messaging:send')
        data = {
            'channel_id': str(self.channel.id),
            'content': 'Test message content',
            'message_type': 'text'
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(Message.objects.filter(channel=self.channel).exists())

    def test_send_message_unauthorized_channel(self):
        """Test sending message to unauthorized channel."""
        # Create a private channel without the user
        private_channel = Channel.objects.create(
            name='private-channel',
            organization=self.organization,
            channel_type='private',
            created_by=self.other_user
        )

        self.client.force_authenticate(user=self.user)
        url = reverse('messaging:send')
        data = {
            'channel_id': str(private_channel.id),
            'content': 'Test message',
            'message_type': 'text'
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_get_channel_messages(self):
        """Test getting channel messages."""
        # Create some messages
        for i in range(3):
            message = Message.objects.create(
                channel=self.channel,
                sender=self.user,
                message_type='text'
            )
            message.set_content(f'Message {i}')

        self.client.force_authenticate(user=self.user)
        url = reverse('messaging:channel_messages', kwargs={'channel_id': self.channel.id})

        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 3)

    def test_edit_message(self):
        """Test editing a message."""
        message = Message.objects.create(
            channel=self.channel,
            sender=self.user,
            message_type='text'
        )
        message.set_content('Original content')

        self.client.force_authenticate(user=self.user)
        url = reverse('messaging:detail', kwargs={'message_id': message.id})
        data = {'content': 'Updated content'}

        response = self.client.patch(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        message.refresh_from_db()
        self.assertEqual(message.get_content(), 'Updated content')
        self.assertTrue(message.is_edited)

    def test_delete_message(self):
        """Test deleting a message."""
        message = Message.objects.create(
            channel=self.channel,
            sender=self.user,
            message_type='text'
        )
        message.set_content('Test content')

        self.client.force_authenticate(user=self.user)
        url = reverse('messaging:detail', kwargs={'message_id': message.id})

        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        message.refresh_from_db()
        self.assertTrue(message.is_deleted)

    def test_add_message_reaction(self):
        """Test adding a reaction to a message."""
        message = Message.objects.create(
            channel=self.channel,
            sender=self.user,
            message_type='text'
        )
        message.set_content('Test content')

        self.client.force_authenticate(user=self.other_user)
        url = reverse('messaging:reactions', kwargs={'message_id': message.id})
        data = {'reaction_type': 'like'}

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(
            MessageReaction.objects.filter(
                message=message,
                user=self.other_user,
                reaction_type='like'
            ).exists()
        )
