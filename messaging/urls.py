from django.urls import path
from . import views

app_name = 'messaging'

urlpatterns = [
    # Message management
    path('send/', views.MessageCreateAPIView.as_view(), name='send'),
    path('<uuid:message_id>/', views.MessageDetailAPIView.as_view(), name='detail'),
    path('<uuid:message_id>/replies/', views.MessageRepliesAPIView.as_view(), name='replies'),
    
    # Channel messages
    path('channel/<uuid:channel_id>/', views.ChannelMessagesAPIView.as_view(), name='channel_messages'),
    path('channel/<uuid:channel_id>/mark-read/', views.MarkChannelMessagesAsReadAPIView.as_view(), name='mark_channel_read'),
    
    # Message reactions
    path('<uuid:message_id>/reactions/', views.MessageReactionAPIView.as_view(), name='reactions'),
    
    # Message status
    path('<uuid:message_id>/status/', views.MessageStatusAPIView.as_view(), name='status'),
    path('<uuid:message_id>/mark-read/', views.MarkMessageAsReadAPIView.as_view(), name='mark_read'),

    # File attachments
    path('<uuid:message_id>/attachments/', views.MessageAttachmentsAPIView.as_view(), name='attachments'),
    path('<uuid:message_id>/upload/', views.MessageAttachmentUploadAPIView.as_view(), name='upload'),
    path('attachments/<uuid:attachment_id>/', views.MessageAttachmentDetailAPIView.as_view(), name='attachment_detail'),
    path('attachments/<uuid:attachment_id>/download/', views.DownloadAttachmentAPIView.as_view(), name='download'),

    # WebSocket endpoints
    path('websocket-info/', views.WebSocketInfoAPIView.as_view(), name='websocket_info'),
    path('channel/<uuid:channel_id>/websocket/', views.ChannelWebSocketUrlAPIView.as_view(), name='channel_websocket'),

    # Search endpoints
    path('search/', views.MessageSearchAPIView.as_view(), name='search'),
    path('search/global/', views.GlobalSearchAPIView.as_view(), name='global_search'),
    path('search/suggestions/', views.SearchSuggestionsAPIView.as_view(), name='search_suggestions'),
]
