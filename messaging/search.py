from django.db.models import Q, Case, When, IntegerField
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
from channels_app.models import Channel, ChannelMembership
from .models import Message

User = get_user_model()


class MessageSearchEngine:
    """Search engine for messages with advanced filtering."""
    
    def __init__(self, user):
        self.user = user
    
    def search(self, query, **filters):
        """
        Search messages with various filters.
        
        Args:
            query (str): Search query
            **filters: Additional filters like channel_id, sender_id, date_from, date_to, etc.
        """
        # Base queryset - only messages user can access
        accessible_channels = self._get_accessible_channels()
        queryset = Message.objects.filter(
            channel__in=accessible_channels,
            is_deleted=False
        )
        
        # Apply text search
        if query:
            queryset = self._apply_text_search(queryset, query)
        
        # Apply filters
        queryset = self._apply_filters(queryset, filters)
        
        # Add relevance scoring
        if query:
            queryset = self._add_relevance_scoring(queryset, query)
        
        return queryset.select_related('sender', 'channel').prefetch_related('attachments')
    
    def _get_accessible_channels(self):
        """Get channels the user can access."""
        return Channel.objects.filter(
            Q(channel_type='public', organization__memberships__user=self.user) |
            Q(memberships__user=self.user, memberships__is_active=True),
            is_active=True
        ).distinct()
    
    def _apply_text_search(self, queryset, query):
        """Apply text search to message content."""
        # Split query into terms
        terms = query.split()
        
        # Build search conditions
        search_conditions = Q()
        for term in terms:
            # Search in decrypted content (this is a simplified approach)
            # In production, you might want to use full-text search capabilities
            search_conditions |= Q(content_encrypted__icontains=term)
        
        return queryset.filter(search_conditions)
    
    def _apply_filters(self, queryset, filters):
        """Apply additional filters to the queryset."""
        # Channel filter
        if 'channel_id' in filters:
            queryset = queryset.filter(channel_id=filters['channel_id'])
        
        # Sender filter
        if 'sender_id' in filters:
            queryset = queryset.filter(sender_id=filters['sender_id'])
        
        # Date range filters
        if 'date_from' in filters:
            date_from = self._parse_date(filters['date_from'])
            if date_from:
                queryset = queryset.filter(created_at__gte=date_from)
        
        if 'date_to' in filters:
            date_to = self._parse_date(filters['date_to'])
            if date_to:
                # Add one day to include the entire day
                date_to = date_to + timedelta(days=1)
                queryset = queryset.filter(created_at__lt=date_to)
        
        # Message type filter
        if 'message_type' in filters:
            queryset = queryset.filter(message_type=filters['message_type'])
        
        # Has attachments filter
        if 'has_attachments' in filters:
            if filters['has_attachments']:
                queryset = queryset.filter(attachments__isnull=False)
            else:
                queryset = queryset.filter(attachments__isnull=True)
        
        # Thread filter
        if 'in_thread' in filters:
            if filters['in_thread']:
                queryset = queryset.filter(parent_message__isnull=False)
            else:
                queryset = queryset.filter(parent_message__isnull=True)
        
        return queryset.distinct()
    
    def _add_relevance_scoring(self, queryset, query):
        """Add relevance scoring to search results."""
        terms = query.split()
        
        # Simple relevance scoring based on term frequency
        # In production, you might want to use more sophisticated scoring
        relevance_cases = []
        
        for i, term in enumerate(terms):
            relevance_cases.append(
                When(content_encrypted__icontains=term, then=len(terms) - i)
            )
        
        return queryset.annotate(
            relevance=Case(
                *relevance_cases,
                default=0,
                output_field=IntegerField()
            )
        ).order_by('-relevance', '-created_at')
    
    def _parse_date(self, date_str):
        """Parse date string to datetime object."""
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return None


class UserSearchEngine:
    """Search engine for users."""
    
    def __init__(self, user):
        self.user = user
    
    def search(self, query, **filters):
        """
        Search users within accessible organizations.
        
        Args:
            query (str): Search query
            **filters: Additional filters like organization_id
        """
        # Get users from accessible organizations
        accessible_users = self._get_accessible_users()
        queryset = accessible_users
        
        # Apply text search
        if query:
            queryset = self._apply_text_search(queryset, query)
        
        # Apply filters
        queryset = self._apply_filters(queryset, filters)
        
        return queryset.distinct()
    
    def _get_accessible_users(self):
        """Get users the current user can see (same organizations)."""
        from organizations.models import Organization
        
        # Get organizations user is member of
        user_orgs = Organization.objects.filter(
            memberships__user=self.user,
            memberships__status='active',
            is_active=True
        )
        
        # Get users from these organizations
        return User.objects.filter(
            organization_memberships__organization__in=user_orgs,
            organization_memberships__status='active',
            is_active=True
        ).exclude(id=self.user.id)
    
    def _apply_text_search(self, queryset, query):
        """Apply text search to user fields."""
        terms = query.split()
        search_conditions = Q()
        
        for term in terms:
            search_conditions |= (
                Q(first_name__icontains=term) |
                Q(last_name__icontains=term) |
                Q(username__icontains=term) |
                Q(email__icontains=term)
            )
        
        return queryset.filter(search_conditions)
    
    def _apply_filters(self, queryset, filters):
        """Apply additional filters to the queryset."""
        # Organization filter
        if 'organization_id' in filters:
            queryset = queryset.filter(
                organization_memberships__organization_id=filters['organization_id'],
                organization_memberships__status='active'
            )
        
        # Online status filter
        if 'is_online' in filters:
            queryset = queryset.filter(is_online=filters['is_online'])
        
        return queryset


class ChannelSearchEngine:
    """Search engine for channels."""
    
    def __init__(self, user):
        self.user = user
    
    def search(self, query, **filters):
        """
        Search channels within accessible organizations.
        
        Args:
            query (str): Search query
            **filters: Additional filters like organization_id, channel_type
        """
        # Get accessible channels
        accessible_channels = self._get_accessible_channels()
        queryset = accessible_channels
        
        # Apply text search
        if query:
            queryset = self._apply_text_search(queryset, query)
        
        # Apply filters
        queryset = self._apply_filters(queryset, filters)
        
        return queryset.distinct()
    
    def _get_accessible_channels(self):
        """Get channels the user can access."""
        return Channel.objects.filter(
            Q(channel_type='public', organization__memberships__user=self.user) |
            Q(memberships__user=self.user, memberships__is_active=True),
            is_active=True,
            is_archived=False
        )
    
    def _apply_text_search(self, queryset, query):
        """Apply text search to channel fields."""
        terms = query.split()
        search_conditions = Q()
        
        for term in terms:
            search_conditions |= (
                Q(name__icontains=term) |
                Q(description__icontains=term)
            )
        
        return queryset.filter(search_conditions)
    
    def _apply_filters(self, queryset, filters):
        """Apply additional filters to the queryset."""
        # Organization filter
        if 'organization_id' in filters:
            queryset = queryset.filter(organization_id=filters['organization_id'])
        
        # Channel type filter
        if 'channel_type' in filters:
            queryset = queryset.filter(channel_type=filters['channel_type'])
        
        return queryset
