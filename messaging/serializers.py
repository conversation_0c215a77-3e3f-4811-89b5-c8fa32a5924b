import mimetypes
from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.db import models
from channels_app.models import Channel
from mycorrmessaging.modules.exceptions import InvalidRequestException
from .models import Message, MessageReaction, MessageStatus, MessageAttachment
from .utils import validate_file_upload, get_file_type, get_image_dimensions

User = get_user_model()


class MessageCreateSerializerIn(serializers.ModelSerializer):
    """Serializer for creating messages."""
    channel_id = serializers.UUIDField(write_only=True)
    content = serializers.CharField(max_length=10000)
    parent_message_id = serializers.UUIDField(required=False, allow_null=True)
    mention_user_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        allow_empty=True
    )
    
    class Meta:
        model = Message
        fields = ['content', 'message_type', 'channel_id', 'parent_message_id', 'mention_user_ids']
    
    def validate_channel_id(self, value):
        try:
            channel = Channel.objects.get(id=value, is_active=True)
            if not channel.can_user_access(self.context['request'].user):
                raise InvalidRequestException({"message": "You don't have access to this channel."})
            self.channel = channel
            return value
        except Channel.DoesNotExist:
            raise InvalidRequestException({"message": "Channel not found."})
    def validate_parent_message_id(self, value):
        if value:
            try:
                parent_message = Message.objects.get(id=value, is_deleted=False)
                if parent_message.channel != self.channel:
                    raise InvalidRequestException({"message": "Parent message must be in the same channel."})
                if not self.channel.allow_threads:
                    raise InvalidRequestException({"message": "Threading is not allowed in this channel."})
                return value
            except Message.DoesNotExist:
                raise InvalidRequestException({"message": "Parent message not found."})
        return value
    
    def validate_mention_user_ids(self, value):
        if value:
            # Check if mentioned users are channel members
            channel_member_ids = set(
                self.channel.memberships.filter(is_active=True).values_list('user_id', flat=True)
            )
            invalid_mentions = set(value) - channel_member_ids
            if invalid_mentions:
                raise InvalidRequestException({"message": "Some mentioned users are not channel members."})
        return value
    
    def create(self, validated_data):
        channel_id = validated_data.pop('channel_id')
        parent_message_id = validated_data.pop('parent_message_id', None)
        mention_user_ids = validated_data.pop('mention_user_ids', [])
        content = validated_data.pop('content')
        
        # Create message
        message = Message.objects.create(
            channel=self.channel,
            sender=self.context['request'].user,
            parent_message_id=parent_message_id,
            **validated_data
        )
        
        # Set encrypted content
        message.set_content(content)
        message.save()
        
        # Add mentions
        if mention_user_ids:
            mentioned_users = User.objects.filter(id__in=mention_user_ids)
            message.mentions.set(mentioned_users)
        
        return message


class MessageUpdateSerializerIn(serializers.ModelSerializer):
    """Serializer for updating messages."""
    content = serializers.CharField(max_length=10000)
    
    class Meta:
        model = Message
        fields = ['content']
    
    def update(self, instance, validated_data):
        content = validated_data.get('content')
        if content:
            instance.set_content(content)
            instance.mark_as_edited()
        return instance


class MessageSerializerOut(serializers.ModelSerializer):
    """Serializer for message output."""
    sender_name = serializers.CharField(source='sender.get_full_name', read_only=True)
    sender_avatar = serializers.CharField(source='sender.avatar', read_only=True)
    content = serializers.SerializerMethodField()
    reactions_count = serializers.SerializerMethodField()
    user_reactions = serializers.SerializerMethodField()
    mentions_data = serializers.SerializerMethodField()
    can_edit = serializers.SerializerMethodField()
    can_delete = serializers.SerializerMethodField()
    
    class Meta:
        model = Message
        fields = [
            'id', 'sender', 'sender_name', 'sender_avatar', 'content',
            'message_type', 'parent_message', 'thread_count', 'is_edited',
            'edited_at', 'is_deleted', 'reactions_count', 'user_reactions',
            'mentions_data', 'can_edit', 'can_delete', 'created_at', 'updated_at'
        ]
    
    def get_content(self, obj):
        if obj.is_deleted:
            return "[Message deleted]"
        return obj.get_content()
    
    def get_reactions_count(self, obj):
        # Group reactions by type and count
        reactions = obj.reactions.values('reaction_type').annotate(
            count=models.Count('id')
        )
        return {r['reaction_type']: r['count'] for r in reactions}
    
    def get_user_reactions(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            user_reactions = obj.reactions.filter(user=request.user).values_list(
                'reaction_type', flat=True
            )
            return list(user_reactions)
        return []
    
    def get_mentions_data(self, obj):
        mentions = obj.mentions.all()
        return [
            {
                'id': user.id,
                'name': user.get_full_name(),
                'username': user.username
            }
            for user in mentions
        ]
    
    def get_can_edit(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.sender == request.user and not obj.is_deleted
        return False
    
    def get_can_delete(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            # User can delete their own messages or channel admins can delete any message
            return (obj.sender == request.user or 
                   obj.channel.is_admin(request.user)) and not obj.is_deleted
        return False


class MessageListSerializerOut(serializers.ModelSerializer):
    """Serializer for message list output (lighter version)."""
    sender_name = serializers.CharField(source='sender.get_full_name', read_only=True)
    sender_avatar = serializers.CharField(source='sender.avatar', read_only=True)
    content = serializers.SerializerMethodField()
    reactions_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Message
        fields = [
            'id', 'sender', 'sender_name', 'sender_avatar', 'content',
            'message_type', 'parent_message', 'thread_count', 'is_edited',
            'is_deleted', 'reactions_count', 'created_at'
        ]
    
    def get_content(self, obj):
        if obj.is_deleted:
            return "[Message deleted]"
        return obj.get_content()
    
    def get_reactions_count(self, obj):
        return obj.reactions.count()


class MessageReactionSerializerIn(serializers.ModelSerializer):
    """Serializer for creating message reactions."""
    class Meta:
        model = MessageReaction
        fields = ['reaction_type']


class MessageReactionSerializerOut(serializers.ModelSerializer):
    """Serializer for message reaction output."""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = MessageReaction
        fields = ['id', 'user', 'user_name', 'reaction_type', 'created_at']


class MessageStatusSerializerOut(serializers.ModelSerializer):
    """Serializer for message status output."""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    
    class Meta:
        model = MessageStatus
        fields = [
            'id', 'user', 'user_name', 'status', 'delivered_at', 'read_at', 'created_at'
        ]


class MessageAttachmentUploadSerializerIn(serializers.Serializer):
    """Serializer for uploading message attachments to cloud storage."""
    file_url = serializers.CharField(help_text="URL of the file in cloud storage")
    original_filename = serializers.CharField(help_text="Original filename")
    file_type = serializers.CharField(help_text="Type of file (image, video, audio, document)")
    file_size = serializers.IntegerField(help_text="File size in bytes")
    mime_type = serializers.CharField(help_text="MIME type of the file")
    width = serializers.IntegerField(required=False, allow_null=True, help_text="Image width in pixels")
    height = serializers.IntegerField(required=False, allow_null=True, help_text="Image height in pixels")

    def create(self, validated_data):
        # Create attachment with cloud storage URL
        attachment = MessageAttachment.objects.create(
            message=self.context['message'],
            file_url=validated_data['file_url'],
            original_filename=validated_data['original_filename'],
            file_type=validated_data['file_type'],
            file_size=validated_data['file_size'],
            mime_type=validated_data['mime_type'],
            width=validated_data.get('width'),
            height=validated_data.get('height')
        )

        return attachment


class MessageAttachmentSerializerOut(serializers.ModelSerializer):
    """Serializer for message attachment output."""
    file_size_human = serializers.ReadOnlyField()
    is_image = serializers.ReadOnlyField()
    is_video = serializers.ReadOnlyField()
    is_audio = serializers.ReadOnlyField()

    class Meta:
        model = MessageAttachment
        fields = [
            'id', 'file', 'original_filename', 'file_type', 'file_size', 'file_size_human',
            'mime_type', 'width', 'height', 'is_image', 'is_video', 'is_audio',
            'created_at'
        ]


class MessageWithAttachmentsSerializerOut(MessageSerializerOut):
    """Extended message serializer that includes attachments."""
    attachments = MessageAttachmentSerializerOut(many=True, read_only=True)

    class Meta(MessageSerializerOut.Meta):
        fields = MessageSerializerOut.Meta.fields + ['attachments']


# Response serializers for function-based views
class MarkMessageAsReadSerializerOut(serializers.Serializer):
    """Serializer for mark message as read response."""
    message = serializers.CharField(help_text="Success message")


class MarkChannelMessagesAsReadSerializerOut(serializers.Serializer):
    """Serializer for mark channel messages as read response."""
    message = serializers.CharField(help_text="Success message")
    count = serializers.IntegerField(help_text="Number of messages marked as read")


class WebSocketInfoSerializerOut(serializers.Serializer):
    """Serializer for WebSocket connection info response."""
    websocket_token = serializers.CharField(help_text="JWT token for WebSocket authentication")
    websocket_urls = serializers.DictField(help_text="WebSocket URLs")
    user_id = serializers.UUIDField(help_text="User ID")
    expires_in = serializers.IntegerField(help_text="Token expiration time in seconds")


class ChannelWebSocketUrlSerializerOut(serializers.Serializer):
    """Serializer for channel WebSocket URL response."""
    websocket_token = serializers.CharField(help_text="JWT token for WebSocket authentication")
    websocket_url = serializers.CharField(help_text="WebSocket URL for the channel")
    channel_id = serializers.UUIDField(help_text="Channel ID")
    user_id = serializers.UUIDField(help_text="User ID")
    expires_in = serializers.IntegerField(help_text="Token expiration time in seconds")


class GlobalSearchSerializerOut(serializers.Serializer):
    """Serializer for global search response."""
    query = serializers.CharField(help_text="Search query")
    results = serializers.DictField(help_text="Search results")
    search_type = serializers.CharField(help_text="Type of search performed")


class SearchSuggestionsSerializerOut(serializers.Serializer):
    """Serializer for search suggestions response."""
    recent_channels = serializers.ListField(help_text="Recent channels")
    recent_users = serializers.ListField(help_text="Recent users")
    popular_terms = serializers.ListField(help_text="Popular search terms")
