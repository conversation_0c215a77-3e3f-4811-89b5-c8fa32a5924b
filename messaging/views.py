from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.db.models import Q, Prefetch
from django.utils import timezone
from drf_spectacular.utils import extend_schema
from mycorrmessaging.modules.exceptions import raise_serializer_error_msg, InvalidRequestException
from mycorrmessaging.modules.paginations import CustomPagination
from channels_app.models import Channel
from .models import Message, MessageReaction, MessageStatus, MessageAttachment
from .serializers import (
    MessageCreateSerializerIn, MessageUpdateSerializerIn,
    MessageSerializerOut, MessageListSerializerOut,
    MessageReactionSerializerIn, MessageReactionSerializerOut,
    MessageStatusSerializerOut, MessageAttachmentUploadSerializerIn,
    MessageAttachmentSerializerOut, MessageWithAttachmentsSerializerOut,
    MarkMessageAsReadSerializerOut, MarkChannelMessagesAsReadSerializerOut,
    WebSocketInfoSerializerOut, ChannelWebSocketUrlSerializerOut,
    GlobalSearchSerializerOut, SearchSuggestionsSerializerOut
)

User = get_user_model()


class MessageCreateAPIView(APIView):
    """API view for creating messages."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=MessageCreateSerializerIn,
        responses={status.HTTP_201_CREATED: MessageSerializerOut}
    )
    def post(self, request):
        serializer = MessageCreateSerializerIn(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        message = serializer.save()
        response_data = MessageSerializerOut(
            message,
            context={'request': request}
        ).data
        response_data['message'] = 'Message sent successfully'

        return Response(response_data, status=status.HTTP_201_CREATED)


class ChannelMessagesAPIView(generics.ListAPIView):
    """API view for listing channel messages."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = MessageListSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        channel_id = self.kwargs['channel_id']
        channel = get_object_or_404(Channel, id=channel_id, is_active=True)

        if not channel.can_user_access(self.request.user):
            raise InvalidRequestException({"message": "You don't have access to this channel."})

        # Get messages that are not deleted and not replies (top-level messages)
        queryset = Message.objects.filter(
            channel=channel,
            is_deleted=False,
            parent_message__isnull=True
        ).select_related('sender').prefetch_related('reactions', 'mentions')

        return queryset.order_by('-created_at')


class MessageDetailAPIView(APIView):
    """API view for message details."""
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self, message_id):
        message = get_object_or_404(Message, id=message_id)

        if not message.channel.can_user_access(self.request.user):
            raise InvalidRequestException({"message": "You don't have access to this message."})

        return message

    @extend_schema(responses={status.HTTP_200_OK: MessageSerializerOut})
    def get(self, request, message_id):
        """Get message details."""
        message = self.get_object(message_id)
        serializer = MessageSerializerOut(
            message,
            context={'request': request}
        )
        return Response(serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        request=MessageUpdateSerializerIn,
        responses={status.HTTP_200_OK: MessageSerializerOut}
    )
    def patch(self, request, message_id):
        """Update message content."""
        message = self.get_object(message_id)

        # Check if user can edit this message
        if message.sender != request.user:
            raise InvalidRequestException({"message": "You can only edit your own messages."})

        if message.is_deleted:
            raise InvalidRequestException({"message": "Cannot edit deleted message."})

        serializer = MessageUpdateSerializerIn(
            message,
            data=request.data,
            partial=True
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        message = serializer.save()
        response_data = MessageSerializerOut(
            message,
            context={'request': request}
        ).data
        response_data['message'] = 'Message updated successfully'

        return Response(response_data, status=status.HTTP_200_OK)

    @extend_schema(responses={status.HTTP_204_NO_CONTENT})
    def delete(self, request, message_id):
        """Delete message."""
        message = self.get_object(message_id)

        # Check if user can delete this message
        can_delete = (
            message.sender == request.user or
            message.channel.is_admin(request.user)
        )

        if not can_delete:
            raise InvalidRequestException({"message": "You don't have permission to delete this message."})

        if message.is_deleted:
            raise InvalidRequestException({"message": "Message is already deleted."})

        message.soft_delete()

        return Response(
            {'message': 'Message deleted successfully'},
            status=status.HTTP_204_NO_CONTENT
        )


class MessageRepliesAPIView(generics.ListAPIView):
    """API view for listing message replies (thread)."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = MessageListSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        message_id = self.kwargs['message_id']
        parent_message = get_object_or_404(Message, id=message_id)

        if not parent_message.channel.can_user_access(self.request.user):
            raise InvalidRequestException({"message": "You don't have access to this message thread."})

        return Message.objects.filter(
            parent_message=parent_message,
            is_deleted=False
        ).select_related('sender').prefetch_related('reactions', 'mentions').order_by('created_at')


class MessageReactionAPIView(APIView):
    """API view for managing message reactions."""
    permission_classes = [permissions.IsAuthenticated]

    def get_message(self, message_id):
        message = get_object_or_404(Message, id=message_id, is_deleted=False)

        if not message.channel.can_user_access(self.request.user):
            raise InvalidRequestException({"message": "You don't have access to this message."})

        return message

    @extend_schema(responses={status.HTTP_200_OK: MessageReactionSerializerOut})
    def get(self, request, message_id):
        """Get message reactions."""
        message = self.get_message(message_id)
        reactions = message.reactions.all().select_related('user')
        serializer = MessageReactionSerializerOut(reactions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        request=MessageReactionSerializerIn,
        responses={status.HTTP_201_CREATED: MessageReactionSerializerOut}
    )
    def post(self, request, message_id):
        """Add reaction to message."""
        message = self.get_message(message_id)

        serializer = MessageReactionSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        reaction_type = serializer.validated_data['reaction_type']

        # Check if user already reacted with this type
        existing_reaction = MessageReaction.objects.filter(
            message=message,
            user=request.user,
            reaction_type=reaction_type
        ).first()

        if existing_reaction:
            return Response(
                {'error': 'You have already reacted with this type'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create reaction
        reaction = MessageReaction.objects.create(
            message=message,
            user=request.user,
            reaction_type=reaction_type
        )

        response_data = MessageReactionSerializerOut(reaction).data
        response_data['message'] = 'Reaction added successfully'

        return Response(response_data, status=status.HTTP_201_CREATED)

    @extend_schema(responses={status.HTTP_204_NO_CONTENT})
    def delete(self, request, message_id):
        """Remove reaction from message."""
        message = self.get_message(message_id)
        reaction_type = request.query_params.get('type')

        if not reaction_type:
            return Response(
                {'error': 'Reaction type is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            reaction = MessageReaction.objects.get(
                message=message,
                user=request.user,
                reaction_type=reaction_type
            )
            reaction.delete()

            return Response(
                {'message': 'Reaction removed successfully'},
                status=status.HTTP_204_NO_CONTENT
            )
        except MessageReaction.DoesNotExist:
            return Response(
                {'error': 'Reaction not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class MessageStatusAPIView(generics.ListAPIView):
    """API view for getting message status."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = MessageStatusSerializerOut

    def get_queryset(self):
        message_id = self.kwargs['message_id']
        message = get_object_or_404(Message, id=message_id)

        # Only message sender or channel admins can view message status
        if (message.sender != self.request.user and
            not message.channel.is_admin(self.request.user)):
            raise InvalidRequestException({"message": "You don't have permission to view message status."})

        return message.statuses.all().select_related('user')


class MarkMessageAsReadAPIView(APIView):
    """API view for marking message as read for the current user."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: MarkMessageAsReadSerializerOut})
    def post(self, request, message_id):
        """Mark message as read for the current user."""
        message = get_object_or_404(Message, id=message_id, is_deleted=False)

        if not message.channel.can_user_access(request.user):
            raise InvalidRequestException({"message": "You don't have access to this message."})

        # Get or create message status
        message_status, created = MessageStatus.objects.get_or_create(
            message=message,
            user=request.user,
            defaults={'status': 'read'}
        )

        if not created and message_status.status != 'read':
            message_status.mark_as_read()

        return Response(
            {'message': 'Message marked as read'},
            status=status.HTTP_200_OK
        )


class MarkChannelMessagesAsReadAPIView(APIView):
    """API view for marking all messages in a channel as read for the current user."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: MarkChannelMessagesAsReadSerializerOut})
    def post(self, request, channel_id):
        """Mark all messages in a channel as read for the current user."""
        channel = get_object_or_404(Channel, id=channel_id, is_active=True)

        if not channel.can_user_access(request.user):
            raise InvalidRequestException({"message": "You don't have access to this channel."})

        # Update all unread message statuses for this user in this channel
        unread_statuses = MessageStatus.objects.filter(
            message__channel=channel,
            user=request.user,
            status__in=['sent', 'delivered']
        )

        count = unread_statuses.count()
        unread_statuses.update(
            status='read',
            read_at=timezone.now()
        )

        # Update channel membership last_read_at
        try:
            membership = channel.memberships.get(user=request.user, is_active=True)
            membership.last_read_at = timezone.now()
            membership.save(update_fields=['last_read_at'])
        except:
            pass

        return Response(
            {'message': f'Marked {count} messages as read', 'count': count},
            status=status.HTTP_200_OK
        )


class MessageAttachmentUploadAPIView(APIView):
    """API view for uploading message attachments."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=MessageAttachmentUploadSerializerIn,
        responses={status.HTTP_201_CREATED: MessageAttachmentSerializerOut}
    )
    def post(self, request, message_id):
        """Upload attachment to message."""
        message = get_object_or_404(Message, id=message_id, is_deleted=False)

        # Check if user can access the message
        if not message.channel.can_user_access(request.user):
            raise InvalidRequestException({"message": "You don't have access to this message."})

        # Check if channel allows file uploads
        if not message.channel.allow_file_uploads:
            return Response(
                {'error': 'File uploads are not allowed in this channel'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Only message sender can add attachments
        if message.sender != request.user:
            raise InvalidRequestException({"message": "You can only add attachments to your own messages."})

        serializer = MessageAttachmentUploadSerializerIn(
            data=request.data,
            context={'message': message, 'request': request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        attachment = serializer.save()

        # Update message type to file if it's currently text
        if message.message_type == 'text':
            message.message_type = 'file'
            message.save(update_fields=['message_type'])

        response_data = MessageAttachmentSerializerOut(
            attachment,
            context={'request': request}
        ).data
        response_data['message'] = 'File uploaded successfully'

        return Response(response_data, status=status.HTTP_201_CREATED)


class MessageAttachmentDetailAPIView(APIView):
    """API view for message attachment details."""
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self, attachment_id):
        attachment = get_object_or_404(MessageAttachment, id=attachment_id)

        if not attachment.message.channel.can_user_access(self.request.user):
            raise InvalidRequestException({"message": "You don't have access to this attachment."})

        return attachment

    @extend_schema(responses={status.HTTP_200_OK: MessageAttachmentSerializerOut})
    def get(self, request, attachment_id):
        """Get attachment details."""
        attachment = self.get_object(attachment_id)
        serializer = MessageAttachmentSerializerOut(
            attachment,
            context={'request': request}
        )
        return Response(serializer.data, status=status.HTTP_200_OK)

    @extend_schema(responses={status.HTTP_204_NO_CONTENT})
    def delete(self, request, attachment_id):
        """Delete attachment."""
        attachment = self.get_object(attachment_id)

        # Only message sender or channel admins can delete attachments
        can_delete = (
            attachment.message.sender == request.user or
            attachment.message.channel.is_admin(request.user)
        )

        if not can_delete:
            raise InvalidRequestException({"message": "You don't have permission to delete this attachment."})

        # Delete the file from storage
        if attachment.file:
            attachment.file.delete(save=False)

        attachment.delete()

        return Response(
            {'message': 'Attachment deleted successfully'},
            status=status.HTTP_204_NO_CONTENT
        )


class MessageAttachmentsAPIView(generics.ListAPIView):
    """API view for listing message attachments."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = MessageAttachmentSerializerOut

    def get_queryset(self):
        message_id = self.kwargs['message_id']
        message = get_object_or_404(Message, id=message_id, is_deleted=False)

        if not message.channel.can_user_access(self.request.user):
            raise InvalidRequestException({"message": "You don't have access to this message."})

        return message.attachments.all()


class DownloadAttachmentAPIView(APIView):
    """API view for downloading attachment files."""
    permission_classes = [permissions.IsAuthenticated]

    # @extend_schema(responses={
    #     status.HTTP_200_OK: serializers.Serializer,
    #     status.HTTP_404_NOT_FOUND: serializers.Serializer
    # })
    def get(self, request, attachment_id):
        """Get attachment download URL."""
        attachment = get_object_or_404(MessageAttachment, id=attachment_id)

        if not attachment.message.channel.can_user_access(request.user):
            raise InvalidRequestException({"message": "You don't have access to this attachment."})

        # Return the cloud storage URL for direct download
        if attachment.file:
            return Response({
                'download_url': attachment.file,
                'filename': attachment.original_filename,
                'file_type': attachment.file_type,
                'file_size': attachment.file_size,
                'mime_type': attachment.mime_type
            }, status=status.HTTP_200_OK)

        return Response(
            {'error': 'File URL not found'},
            status=status.HTTP_404_NOT_FOUND
        )


class WebSocketInfoAPIView(APIView):
    """API view for getting WebSocket connection information."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: WebSocketInfoSerializerOut})
    def get(self, request):
        """Get WebSocket connection information."""
        from rest_framework_simplejwt.tokens import RefreshToken

        # Generate a token for WebSocket authentication
        refresh = RefreshToken.for_user(request.user)
        access_token = str(refresh.access_token)

        # Get WebSocket URLs
        protocol = 'wss' if request.is_secure() else 'ws'
        host = request.get_host()

        return Response({
            'websocket_token': access_token,
            'websocket_urls': {
                'user': f'{protocol}://{host}/ws/user/{request.user.id}/?token={access_token}',
            },
            'user_id': str(request.user.id),
            'expires_in': 3600  # Token expires in 1 hour
        }, status=status.HTTP_200_OK)


class ChannelWebSocketUrlAPIView(APIView):
    """API view for getting WebSocket URL for a specific channel."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: ChannelWebSocketUrlSerializerOut})
    def get(self, request, channel_id):
        """Get WebSocket URL for a specific channel."""
        from rest_framework_simplejwt.tokens import RefreshToken

        # Check if user can access the channel
        channel = get_object_or_404(Channel, id=channel_id, is_active=True)
        if not channel.can_user_access(request.user):
            raise InvalidRequestException({"message": "You don't have access to this channel."})

        # Generate a token for WebSocket authentication
        refresh = RefreshToken.for_user(request.user)
        access_token = str(refresh.access_token)

        # Get WebSocket URL
        protocol = 'wss' if request.is_secure() else 'ws'
        host = request.get_host()

        return Response({
            'websocket_token': access_token,
            'websocket_url': f'{protocol}://{host}/ws/channel/{channel_id}/?token={access_token}',
            'channel_id': str(channel_id),
            'user_id': str(request.user.id),
            'expires_in': 3600  # Token expires in 1 hour
        }, status=status.HTTP_200_OK)


class GlobalSearchAPIView(APIView):
    """API view for global search across messages, users, and channels."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK})
    def get(self, request):
        """Perform global search."""
        query = request.query_params.get('q', '').strip()
        search_type = request.query_params.get('type', 'all')  # all, messages, users, channels

        if not query:
            return Response(
                {'error': 'Search query is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        results = {}

        # Search messages
        if search_type in ['all', 'messages']:
            from .search import MessageSearchEngine
            message_engine = MessageSearchEngine(request.user)

            # Get filters from query params
            filters = {}
            if request.query_params.get('channel_id'):
                filters['channel_id'] = request.query_params.get('channel_id')
            if request.query_params.get('sender_id'):
                filters['sender_id'] = request.query_params.get('sender_id')
            if request.query_params.get('date_from'):
                filters['date_from'] = request.query_params.get('date_from')
            if request.query_params.get('date_to'):
                filters['date_to'] = request.query_params.get('date_to')
            if request.query_params.get('message_type'):
                filters['message_type'] = request.query_params.get('message_type')
            if request.query_params.get('has_attachments'):
                filters['has_attachments'] = request.query_params.get('has_attachments').lower() == 'true'

            messages = message_engine.search(query, **filters)[:10]  # Limit to 10 results
            results['messages'] = MessageListSerializerOut(messages, many=True, context={'request': request}).data

        # Search users
        if search_type in ['all', 'users']:
            from .search import UserSearchEngine
            from accounts.serializers import UserSearchSerializerOut

            user_engine = UserSearchEngine(request.user)
            filters = {}
            if request.query_params.get('organization_id'):
                filters['organization_id'] = request.query_params.get('organization_id')

            users = user_engine.search(query, **filters)[:10]  # Limit to 10 results
            results['users'] = UserSearchSerializerOut(users, many=True, context={'request': request}).data

        # Search channels
        if search_type in ['all', 'channels']:
            from .search import ChannelSearchEngine
            from channels_app.serializers import ChannelListSerializerOut

            channel_engine = ChannelSearchEngine(request.user)
            filters = {}
            if request.query_params.get('organization_id'):
                filters['organization_id'] = request.query_params.get('organization_id')
            if request.query_params.get('channel_type'):
                filters['channel_type'] = request.query_params.get('channel_type')

            channels = channel_engine.search(query, **filters)[:10]  # Limit to 10 results
            results['channels'] = ChannelListSerializerOut(channels, many=True, context={'request': request}).data

        return Response({
            'query': query,
            'results': results,
            'search_type': search_type
        }, status=status.HTTP_200_OK)


class MessageSearchAPIView(generics.ListAPIView):
    """API view for searching messages with pagination."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = MessageListSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        query = self.request.query_params.get('q', '').strip()

        if not query:
            return Message.objects.none()

        from .search import MessageSearchEngine
        engine = MessageSearchEngine(self.request.user)

        # Get filters from query params
        filters = {}
        for param in ['channel_id', 'sender_id', 'date_from', 'date_to', 'message_type']:
            if self.request.query_params.get(param):
                filters[param] = self.request.query_params.get(param)

        if self.request.query_params.get('has_attachments'):
            filters['has_attachments'] = self.request.query_params.get('has_attachments').lower() == 'true'

        if self.request.query_params.get('in_thread'):
            filters['in_thread'] = self.request.query_params.get('in_thread').lower() == 'true'

        return engine.search(query, **filters)


class SearchSuggestionsAPIView(APIView):
    """API view for getting search suggestions based on user's recent activity."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: SearchSuggestionsSerializerOut})
    def get(self, request):
        """Get search suggestions based on user's recent activity."""
        suggestions = {
            'recent_channels': [],
            'recent_users': [],
            'popular_terms': []
        }

        # Get recent channels user has messaged in
        recent_channels = Channel.objects.filter(
            messages__sender=request.user,
            is_active=True
        ).distinct().order_by('-messages__created_at')[:5]

        from channels_app.serializers import ChannelListSerializerOut
        suggestions['recent_channels'] = ChannelListSerializerOut(
            recent_channels,
            many=True,
            context={'request': request}
        ).data

        # Get users from same organizations
        from .search import UserSearchEngine
        from accounts.serializers import UserSearchSerializerOut

        user_engine = UserSearchEngine(request.user)
        recent_users = user_engine._get_accessible_users().filter(is_online=True)[:5]
        suggestions['recent_users'] = UserSearchSerializerOut(
            recent_users,
            many=True,
            context={'request': request}
        ).data

        # Popular search terms could be implemented with analytics
        suggestions['popular_terms'] = ['meeting', 'project', 'update', 'help', 'question']

        return Response(suggestions, status=status.HTTP_200_OK)
