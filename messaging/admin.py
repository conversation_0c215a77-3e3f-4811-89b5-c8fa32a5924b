from django.contrib import admin
from django.utils.html import format_html
from .models import Message, MessageReaction, MessageStatus, MessageAttachment


class MessageReactionInline(admin.TabularInline):
    """Inline admin for message reactions."""
    model = MessageReaction
    extra = 0
    readonly_fields = ['created_at']
    fields = ['user', 'reaction_type', 'created_at']


class MessageStatusInline(admin.TabularInline):
    """Inline admin for message statuses."""
    model = MessageStatus
    extra = 0
    readonly_fields = ['created_at', 'delivered_at', 'read_at']
    fields = ['user', 'status', 'delivered_at', 'read_at']


class MessageAttachmentInline(admin.TabularInline):
    """Inline admin for message attachments."""
    model = MessageAttachment
    extra = 0
    readonly_fields = ['created_at', 'file_size', 'file_size_human', 'mime_type']
    fields = ['original_filename', 'file_type', 'file_size_human', 'mime_type']


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    """Admin configuration for Message model."""

    list_display = [
        'content_preview', 'sender', 'channel', 'message_type',
        'is_edited', 'is_deleted', 'thread_count', 'created_at'
    ]
    list_filter = [
        'message_type', 'is_edited', 'is_deleted', 'created_at',
        'channel__organization'
    ]
    search_fields = [
        'sender__email', 'sender__first_name', 'sender__last_name',
        'channel__name', 'channel__organization__name'
    ]
    readonly_fields = [
        'id', 'content_preview', 'thread_count', 'created_at', 'updated_at',
        'edited_at', 'deleted_at'
    ]
    inlines = [MessageReactionInline, MessageStatusInline, MessageAttachmentInline]

    fieldsets = (
        (None, {
            'fields': ('channel', 'sender', 'message_type', 'content_preview')
        }),
        ('Threading', {
            'fields': ('parent_message', 'thread_count')
        }),
        ('Status', {
            'fields': ('is_edited', 'edited_at', 'is_deleted', 'deleted_at')
        }),
        ('Mentions', {
            'fields': ('mentions',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def content_preview(self, obj):
        content = obj.get_content()
        if obj.is_deleted:
            return "[Message deleted]"
        return content[:100] + "..." if len(content) > 100 else content
    content_preview.short_description = "Content Preview"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'sender', 'channel', 'channel__organization', 'parent_message'
        ).prefetch_related('mentions')


@admin.register(MessageReaction)
class MessageReactionAdmin(admin.ModelAdmin):
    """Admin configuration for MessageReaction model."""

    list_display = [
        'message_preview', 'user', 'reaction_type', 'created_at'
    ]
    list_filter = ['reaction_type', 'created_at']
    search_fields = [
        'user__email', 'user__first_name', 'user__last_name',
        'message__sender__email'
    ]
    readonly_fields = ['id', 'created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('message', 'user', 'reaction_type')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def message_preview(self, obj):
        content = obj.message.get_content()
        return content[:50] + "..." if len(content) > 50 else content
    message_preview.short_description = "Message"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'message', 'message__sender')


@admin.register(MessageStatus)
class MessageStatusAdmin(admin.ModelAdmin):
    """Admin configuration for MessageStatus model."""

    list_display = [
        'message_preview', 'user', 'status', 'delivered_at', 'read_at'
    ]
    list_filter = ['status', 'created_at', 'delivered_at', 'read_at']
    search_fields = [
        'user__email', 'user__first_name', 'user__last_name',
        'message__sender__email'
    ]
    readonly_fields = ['id', 'created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('message', 'user', 'status')
        }),
        ('Timing', {
            'fields': ('delivered_at', 'read_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def message_preview(self, obj):
        content = obj.message.get_content()
        return content[:50] + "..." if len(content) > 50 else content
    message_preview.short_description = "Message"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'message', 'message__sender')


@admin.register(MessageAttachment)
class MessageAttachmentAdmin(admin.ModelAdmin):
    """Admin configuration for MessageAttachment model."""

    list_display = [
        'original_filename', 'message_preview', 'file_type',
        'file_size_human', 'is_scanned', 'created_at'
    ]
    list_filter = ['file_type', 'is_scanned', 'created_at']
    search_fields = [
        'original_filename', 'message__sender__email',
        'message__channel__name'
    ]
    readonly_fields = [
        'id', 'file_size', 'file_size_human', 'mime_type',
        'width', 'height', 'created_at', 'updated_at'
    ]

    fieldsets = (
        (None, {
            'fields': ('message', 'file', 'original_filename', 'file_type')
        }),
        ('File Info', {
            'fields': ('file_size', 'file_size_human', 'mime_type', 'width', 'height')
        }),
        ('Security', {
            'fields': ('is_scanned', 'scan_result')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def message_preview(self, obj):
        content = obj.message.get_content()
        return content[:50] + "..." if len(content) > 50 else content
    message_preview.short_description = "Message"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('message', 'message__sender')
