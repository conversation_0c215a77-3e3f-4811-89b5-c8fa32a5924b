from django.db import models
from django.conf import settings
from django.utils import timezone
from home.models import BaseModel
from channels_app.models import Channel
from mycorrmessaging.modules.choices import (
    MESSAGE_TYPE_CHOICES, MESSAGE_STATUS_CHOICES, REACTION_TYPE_CHOICES,
    FILE_TYPE_CHOICES
)
from mycorrmessaging.modules.utils import encrypt_text, decrypt_text


class Message(BaseModel):
    """
    Model representing a message in a channel.
    """
    channel = models.ForeignKey(
        Channel,
        on_delete=models.CASCADE,
        related_name='messages',
        help_text="Channel this message belongs to"
    )
    sender = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='sent_messages',
        help_text="User who sent this message"
    )
    content_encrypted = models.TextField(
        help_text="Encrypted message content"
    )
    message_type = models.CharField(
        max_length=20,
        choices=MESSAGE_TYPE_CHOICES,
        default='text',
        help_text="Type of message"
    )

    # Threading support
    parent_message = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='replies',
        help_text="Parent message for threaded replies"
    )
    thread_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of replies in this thread"
    )

    # Message status
    is_edited = models.BooleanField(
        default=False,
        help_text="Whether message has been edited"
    )
    edited_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When message was last edited"
    )
    is_deleted = models.BooleanField(
        default=False,
        help_text="Whether message is deleted (soft delete)"
    )
    deleted_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When message was deleted"
    )

    # Mentions
    mentions = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name='mentioned_in_messages',
        help_text="Users mentioned in this message"
    )

    class Meta:
        db_table = 'messaging_message'
        verbose_name = 'Message'
        verbose_name_plural = 'Messages'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['channel', '-created_at']),
            models.Index(fields=['sender', '-created_at']),
            models.Index(fields=['parent_message', '-created_at']),
        ]

    def __str__(self):
        content_preview = self.get_content()[:50]
        return f"{self.sender.get_full_name()}: {content_preview}..."

    def get_content(self):
        """Decrypt and return message content."""
        if self.content_encrypted:
            return decrypt_text(self.content_encrypted)
        return ""

    def set_content(self, content):
        """Encrypt and set message content."""
        self.content_encrypted = encrypt_text(content)

    @property
    def content(self):
        """Property to get decrypted content."""
        return self.get_content()

    @content.setter
    def content(self, value):
        """Property to set encrypted content."""
        self.set_content(value)

    def mark_as_edited(self):
        """Mark message as edited."""
        self.is_edited = True
        self.edited_at = timezone.now()
        self.save(update_fields=['is_edited', 'edited_at'])

    def soft_delete(self):
        """Soft delete the message."""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save(update_fields=['is_deleted', 'deleted_at'])

    def increment_thread_count(self):
        """Increment thread count for parent message."""
        if self.parent_message:
            self.parent_message.thread_count += 1
            self.parent_message.save(update_fields=['thread_count'])

    def save(self, *args, **kwargs):
        # Increment thread count if this is a reply
        is_new = self.pk is None
        super().save(*args, **kwargs)
        if is_new and self.parent_message:
            self.increment_thread_count()


class MessageReaction(BaseModel):
    """
    Model representing reactions to messages.
    """
    message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        related_name='reactions',
        help_text="Message being reacted to"
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='message_reactions',
        help_text="User who reacted"
    )
    reaction_type = models.CharField(
        max_length=20,
        choices=REACTION_TYPE_CHOICES,
        help_text="Type of reaction"
    )

    class Meta:
        db_table = 'messaging_message_reaction'
        verbose_name = 'Message Reaction'
        verbose_name_plural = 'Message Reactions'
        unique_together = ['message', 'user', 'reaction_type']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.get_full_name()} reacted {self.reaction_type} to message"


class MessageStatus(BaseModel):
    """
    Model to track message delivery and read status for users.
    """
    message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        related_name='statuses',
        help_text="Message"
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='message_statuses',
        help_text="User"
    )
    status = models.CharField(
        max_length=20,
        choices=MESSAGE_STATUS_CHOICES,
        default='sent',
        help_text="Message status for this user"
    )
    delivered_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When message was delivered to user"
    )
    read_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When message was read by user"
    )

    class Meta:
        db_table = 'messaging_message_status'
        verbose_name = 'Message Status'
        verbose_name_plural = 'Message Statuses'
        unique_together = ['message', 'user']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.message} - {self.user.get_full_name()}: {self.status}"

    def mark_as_delivered(self):
        """Mark message as delivered."""
        if self.status == 'sent':
            self.status = 'delivered'
            self.delivered_at = timezone.now()
            self.save(update_fields=['status', 'delivered_at'])

    def mark_as_read(self):
        """Mark message as read."""
        if self.status in ['sent', 'delivered']:
            self.status = 'read'
            self.read_at = timezone.now()
            if not self.delivered_at:
                self.delivered_at = self.read_at
            self.save(update_fields=['status', 'read_at', 'delivered_at'])


class MessageAttachment(BaseModel):
    """
    Model representing file attachments to messages.
    """
    message = models.ForeignKey(
        Message,
        on_delete=models.CASCADE,
        related_name='attachments',
        help_text="Message this attachment belongs to"
    )
    file = models.TextField(
        null=True,
        blank=True,
        help_text="URL to file stored in cloud storage"
    )
    original_filename = models.CharField(
        max_length=255,
        help_text="Original filename"
    )
    file_type = models.CharField(
        max_length=20,
        choices=FILE_TYPE_CHOICES,
        help_text="Type of file"
    )
    file_size = models.PositiveIntegerField(
        help_text="File size in bytes"
    )
    mime_type = models.CharField(
        max_length=100,
        help_text="MIME type of the file"
    )

    # Image-specific fields
    width = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Image width in pixels"
    )
    height = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Image height in pixels"
    )

    # Security
    is_scanned = models.BooleanField(
        default=False,
        help_text="Whether file has been scanned for viruses"
    )
    scan_result = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        help_text="Virus scan result"
    )

    class Meta:
        db_table = 'messaging_message_attachment'
        verbose_name = 'Message Attachment'
        verbose_name_plural = 'Message Attachments'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.original_filename} - {self.message}"

    @property
    def file_size_human(self):
        """Return human-readable file size."""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    @property
    def is_image(self):
        """Check if file is an image."""
        return self.file_type == 'image'

    @property
    def is_video(self):
        """Check if file is a video."""
        return self.file_type == 'video'

    @property
    def is_audio(self):
        """Check if file is audio."""
        return self.file_type == 'audio'

