# Generated by Django 5.2.5 on 2025-08-08 08:49

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('channels_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content_encrypted', models.TextField(help_text='Encrypted message content')),
                ('message_type', models.CharField(choices=[('text', 'Text'), ('file', 'File'), ('image', 'Image'), ('video', 'Video'), ('audio', 'Audio'), ('system', 'System')], default='text', help_text='Type of message', max_length=20)),
                ('thread_count', models.PositiveIntegerField(default=0, help_text='Number of replies in this thread')),
                ('is_edited', models.BooleanField(default=False, help_text='Whether message has been edited')),
                ('edited_at', models.DateTimeField(blank=True, help_text='When message was last edited', null=True)),
                ('is_deleted', models.BooleanField(default=False, help_text='Whether message is deleted (soft delete)')),
                ('deleted_at', models.DateTimeField(blank=True, help_text='When message was deleted', null=True)),
                ('channel', models.ForeignKey(help_text='Channel this message belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='channels_app.channel')),
                ('mentions', models.ManyToManyField(blank=True, help_text='Users mentioned in this message', related_name='mentioned_in_messages', to=settings.AUTH_USER_MODEL)),
                ('parent_message', models.ForeignKey(blank=True, help_text='Parent message for threaded replies', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='messaging.message')),
                ('sender', models.ForeignKey(help_text='User who sent this message', on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Message',
                'verbose_name_plural': 'Messages',
                'db_table': 'messaging_message',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MessageReaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('reaction_type', models.CharField(choices=[('like', '👍'), ('love', '❤️'), ('laugh', '😂'), ('wow', '😮'), ('sad', '😢'), ('angry', '😠'), ('thumbs_up', '👍'), ('thumbs_down', '👎'), ('fire', '🔥'), ('party', '🎉')], help_text='Type of reaction', max_length=20)),
                ('message', models.ForeignKey(help_text='Message being reacted to', on_delete=django.db.models.deletion.CASCADE, related_name='reactions', to='messaging.message')),
                ('user', models.ForeignKey(help_text='User who reacted', on_delete=django.db.models.deletion.CASCADE, related_name='message_reactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Message Reaction',
                'verbose_name_plural': 'Message Reactions',
                'db_table': 'messaging_message_reaction',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MessageStatus',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('sent', 'Sent'), ('delivered', 'Delivered'), ('read', 'Read'), ('failed', 'Failed')], default='sent', help_text='Message status for this user', max_length=20)),
                ('delivered_at', models.DateTimeField(blank=True, help_text='When message was delivered to user', null=True)),
                ('read_at', models.DateTimeField(blank=True, help_text='When message was read by user', null=True)),
                ('message', models.ForeignKey(help_text='Message', on_delete=django.db.models.deletion.CASCADE, related_name='statuses', to='messaging.message')),
                ('user', models.ForeignKey(help_text='User', on_delete=django.db.models.deletion.CASCADE, related_name='message_statuses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Message Status',
                'verbose_name_plural': 'Message Statuses',
                'db_table': 'messaging_message_status',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['channel', '-created_at'], name='messaging_m_channel_54818b_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['sender', '-created_at'], name='messaging_m_sender__9b0bb2_idx'),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['parent_message', '-created_at'], name='messaging_m_parent__8a4cc8_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='messagereaction',
            unique_together={('message', 'user', 'reaction_type')},
        ),
        migrations.AlterUniqueTogether(
            name='messagestatus',
            unique_together={('message', 'user')},
        ),
    ]
