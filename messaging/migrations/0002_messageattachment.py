# Generated by Django 5.2.5 on 2025-08-08 09:03

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('messaging', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MessageAttachment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('file', models.FileField(help_text='Uploaded file', upload_to='message_attachments/%Y/%m/%d/')),
                ('original_filename', models.CharField(help_text='Original filename', max_length=255)),
                ('file_type', models.CharField(choices=[('image', 'Image'), ('video', 'Video'), ('audio', 'Audio'), ('document', 'Document'), ('archive', 'Archive'), ('other', 'Other')], help_text='Type of file', max_length=20)),
                ('file_size', models.PositiveIntegerField(help_text='File size in bytes')),
                ('mime_type', models.CharField(help_text='MIME type of the file', max_length=100)),
                ('width', models.PositiveIntegerField(blank=True, help_text='Image width in pixels', null=True)),
                ('height', models.PositiveIntegerField(blank=True, help_text='Image height in pixels', null=True)),
                ('is_scanned', models.BooleanField(default=False, help_text='Whether file has been scanned for viruses')),
                ('scan_result', models.CharField(blank=True, help_text='Virus scan result', max_length=50, null=True)),
                ('message', models.ForeignKey(help_text='Message this attachment belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='messaging.message')),
            ],
            options={
                'verbose_name': 'Message Attachment',
                'verbose_name_plural': 'Message Attachments',
                'db_table': 'messaging_message_attachment',
                'ordering': ['-created_at'],
            },
        ),
    ]
