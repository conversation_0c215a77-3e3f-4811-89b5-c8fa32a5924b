import os
import mimetypes
from PIL import Image
from django.conf import settings

from mycorrmessaging.modules.exceptions import InvalidRequestException


def get_file_type(file):
    """Determine file type based on file extension and content."""
    # Get MIME type from filename
    mime_type, _ = mimetypes.guess_type(file.name)
    if not mime_type:
        mime_type = 'application/octet-stream'
    
    if mime_type.startswith('image/'):
        return 'image'
    elif mime_type.startswith('video/'):
        return 'video'
    elif mime_type.startswith('audio/'):
        return 'audio'
    elif mime_type in [
        'application/pdf', 'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain', 'text/csv'
    ]:
        return 'document'
    elif mime_type in [
        'application/zip', 'application/x-rar-compressed',
        'application/x-tar', 'application/gzip'
    ]:
        return 'archive'
    else:
        return 'other'


def validate_file_upload(file):
    """Validate uploaded file."""
    # Check file size
    max_size = getattr(settings, 'FILE_UPLOAD_MAX_MEMORY_SIZE', 10 * 1024 * 1024)  # 10MB default
    if file.size > max_size:
        raise InvalidRequestException({'message': f'File size cannot exceed {max_size // (1024 * 1024)}MB'})

    # Check file type
    mime_type, _ = mimetypes.guess_type(file.name)
    if not mime_type:
        mime_type = 'application/octet-stream'
    
    # Define allowed MIME types
    allowed_types = [
        # Images
        'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
        # Videos
        'video/mp4', 'video/mpeg', 'video/quicktime', 'video/x-msvideo',
        # Audio
        'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp4',
        # Documents
        'application/pdf', 'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain', 'text/csv',
        # Archives
        'application/zip', 'application/x-rar-compressed',
        'application/x-tar', 'application/gzip'
    ]
    
    if mime_type not in allowed_types:
        raise InvalidRequestException({'message': f'File type {mime_type} is not allowed'})
    
    return True


def get_image_dimensions(file):
    """Get image dimensions if file is an image."""
    try:
        with Image.open(file) as img:
            return img.width, img.height
    except Exception:
        return None, None


def scan_file_for_viruses(file_path):
    """
    Scan file for viruses. This is a placeholder implementation.
    In production, you would integrate with a real antivirus service.
    """
    # Placeholder implementation
    # In production, integrate with services like:
    # - ClamAV
    # - VirusTotal API
    # - AWS GuardDuty
    # - Azure Defender
    
    # For now, just return clean
    return True, "clean"


def generate_secure_filename(original_filename):
    """Generate a secure filename."""
    import uuid
    import os
    
    # Get file extension
    name, ext = os.path.splitext(original_filename)
    
    # Generate UUID-based filename
    secure_name = f"{uuid.uuid4().hex}{ext.lower()}"
    
    return secure_name


def create_thumbnail(image_file, size=(150, 150)):
    """Create thumbnail for image files."""
    try:
        with Image.open(image_file) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # Create thumbnail
            img.thumbnail(size, Image.Resampling.LANCZOS)
            
            # Save thumbnail
            import io
            thumbnail_io = io.BytesIO()
            img.save(thumbnail_io, format='JPEG', quality=85)
            thumbnail_io.seek(0)
            
            return thumbnail_io
    except Exception:
        return None


# WebSocket utilities
def send_message_to_channel(channel_id, message_data):
    """Send message to channel WebSocket group."""
    from channels.layers import get_channel_layer
    from asgiref.sync import async_to_sync

    channel_layer = get_channel_layer()
    group_name = f'channel_{channel_id}'

    async_to_sync(channel_layer.group_send)(
        group_name,
        {
            'type': 'new_message',
            **message_data
        }
    )


def send_message_update_to_channel(channel_id, message_data):
    """Send message update to channel WebSocket group."""
    from channels.layers import get_channel_layer
    from asgiref.sync import async_to_sync

    channel_layer = get_channel_layer()
    group_name = f'channel_{channel_id}'

    async_to_sync(channel_layer.group_send)(
        group_name,
        {
            'type': 'message_updated',
            **message_data
        }
    )


def send_message_deletion_to_channel(channel_id, message_id):
    """Send message deletion to channel WebSocket group."""
    from channels.layers import get_channel_layer
    from asgiref.sync import async_to_sync
    from django.utils import timezone

    channel_layer = get_channel_layer()
    group_name = f'channel_{channel_id}'

    async_to_sync(channel_layer.group_send)(
        group_name,
        {
            'type': 'message_deleted',
            'message_id': str(message_id),
            'timestamp': timezone.now().isoformat()
        }
    )


def send_notification_to_user(user_id, notification_data):
    """Send notification to user WebSocket group."""
    from channels.layers import get_channel_layer
    from asgiref.sync import async_to_sync

    channel_layer = get_channel_layer()
    group_name = f'user_{user_id}'

    async_to_sync(channel_layer.group_send)(
        group_name,
        {
            'type': 'notification',
            **notification_data
        }
    )


def send_user_status_update_to_channels(user_id, status_data):
    """Send user status update to all channels the user is in."""
    from channels.layers import get_channel_layer
    from asgiref.sync import async_to_sync
    from channels_app.models import ChannelMembership

    channel_layer = get_channel_layer()

    # Get all channels the user is a member of
    memberships = ChannelMembership.objects.filter(
        user_id=user_id,
        is_active=True
    ).select_related('channel')

    for membership in memberships:
        group_name = f'channel_{membership.channel.id}'
        async_to_sync(channel_layer.group_send)(
            group_name,
            {
                'type': 'user_status_update',
                **status_data
            }
        )
