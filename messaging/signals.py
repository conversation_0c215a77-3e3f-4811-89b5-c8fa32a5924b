from django.db.models.signals import post_save, post_delete, m2m_changed
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from channels_app.models import ChannelMembership
from .models import Message, MessageStatus
from .utils import (
    send_message_to_channel, send_message_update_to_channel,
    send_message_deletion_to_channel, send_notification_to_user
)
from .serializers import MessageSerializerOut

User = get_user_model()


@receiver(post_save, sender=Message)
def create_message_statuses(sender, instance, created, **kwargs):
    """
    Create MessageStatus objects for all channel members when a new message is created.
    """
    if created and not instance.is_deleted:
        # Get all active channel members except the sender
        channel_members = ChannelMembership.objects.filter(
            channel=instance.channel,
            is_active=True
        ).exclude(user=instance.sender)
        
        # Create message status for each member
        message_statuses = []
        for membership in channel_members:
            message_statuses.append(
                MessageStatus(
                    message=instance,
                    user=membership.user,
                    status='sent'
                )
            )
        
        if message_statuses:
            MessageStatus.objects.bulk_create(message_statuses)

        # Send real-time notification to channel
        try:
            serializer = MessageSerializerOut(instance)
            message_data = serializer.data
            send_message_to_channel(str(instance.channel.id), {
                'message': message_data,
                'timestamp': instance.created_at.isoformat()
            })
        except Exception:
            # Log error but don't fail the message creation
            pass


@receiver(post_save, sender=Message)
def handle_message_update(sender, instance, created, **kwargs):
    """
    Handle message updates (edits) and send real-time notifications.
    """
    if not created and instance.is_edited:
        try:
            serializer = MessageSerializerOut(instance)
            message_data = serializer.data
            send_message_update_to_channel(str(instance.channel.id), {
                'message': message_data,
                'timestamp': instance.updated_at.isoformat()
            })
        except Exception:
            pass


@receiver(post_save, sender=Message)
def handle_message_soft_delete(sender, instance, created, **kwargs):
    """
    Handle message soft deletion and send real-time notifications.
    """
    if not created and instance.is_deleted:
        try:
            send_message_deletion_to_channel(
                str(instance.channel.id),
                str(instance.id)
            )
        except Exception:
            pass


@receiver(m2m_changed, sender=Message.mentions.through)
def handle_message_mentions(sender, instance, action, pk_set, **kwargs):
    """
    Handle message mentions - send notifications to mentioned users.
    """
    if action == 'post_add' and pk_set:
        try:
            from notifications.utils import create_mention_notification
            mentioned_users = User.objects.filter(id__in=pk_set)
            for user in mentioned_users:
                create_mention_notification(instance, user)
        except Exception:
            pass


@receiver(post_save, sender=Message)
def handle_direct_message_notification(sender, instance, created, **kwargs):
    """
    Handle direct message notifications.
    """
    if created and instance.channel.channel_type == 'direct':
        try:
            from notifications.utils import create_direct_message_notification
            # Get channel participants except sender
            participants = instance.channel.participants.exclude(id=instance.sender.id)
            for participant in participants:
                create_direct_message_notification(instance, participant)
        except Exception:
            pass
