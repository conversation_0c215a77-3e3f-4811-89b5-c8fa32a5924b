import json
import uuid
from datetime import datetime
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone
from channels_app.models import Channel, ChannelMembership
from .models import Message, MessageStatus
from .serializers import MessageSerializerOut

User = get_user_model()


class ChannelConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for channel-based real-time communication."""
    
    async def connect(self):
        self.channel_id = self.scope['url_route']['kwargs']['channel_id']
        self.channel_group_name = f'channel_{self.channel_id}'
        self.user = self.scope['user']
        
        # Check if user is authenticated
        if not self.user.is_authenticated:
            await self.close()
            return
        
        # Check if user can access the channel
        can_access = await self.check_channel_access()
        if not can_access:
            await self.close()
            return
        
        # Join channel group
        await self.channel_layer.group_add(
            self.channel_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Notify others that user joined
        await self.channel_layer.group_send(
            self.channel_group_name,
            {
                'type': 'user_joined',
                'user_id': str(self.user.id),
                'user_name': self.user.get_full_name(),
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def disconnect(self, close_code):
        # Notify others that user left
        if hasattr(self, 'channel_group_name'):
            await self.channel_layer.group_send(
                self.channel_group_name,
                {
                    'type': 'user_left',
                    'user_id': str(self.user.id),
                    'user_name': self.user.get_full_name(),
                    'timestamp': timezone.now().isoformat()
                }
            )
            
            # Leave channel group
            await self.channel_layer.group_discard(
                self.channel_group_name,
                self.channel_name
            )
    
    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'typing_start':
                await self.handle_typing_start()
            elif message_type == 'typing_stop':
                await self.handle_typing_stop()
            elif message_type == 'message_read':
                await self.handle_message_read(data.get('message_id'))
            elif message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': timezone.now().isoformat()
                }))
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON'
            }))
    
    async def handle_typing_start(self):
        """Handle typing start event."""
        await self.channel_layer.group_send(
            self.channel_group_name,
            {
                'type': 'typing_indicator',
                'action': 'start',
                'user_id': str(self.user.id),
                'user_name': self.user.get_full_name(),
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def handle_typing_stop(self):
        """Handle typing stop event."""
        await self.channel_layer.group_send(
            self.channel_group_name,
            {
                'type': 'typing_indicator',
                'action': 'stop',
                'user_id': str(self.user.id),
                'user_name': self.user.get_full_name(),
                'timestamp': timezone.now().isoformat()
            }
        )
    
    async def handle_message_read(self, message_id):
        """Handle message read event."""
        if message_id:
            await self.mark_message_as_read(message_id)
    
    # Event handlers for group messages
    async def new_message(self, event):
        """Send new message to WebSocket."""
        await self.send(text_data=json.dumps(event))
    
    async def message_updated(self, event):
        """Send message update to WebSocket."""
        await self.send(text_data=json.dumps(event))
    
    async def message_deleted(self, event):
        """Send message deletion to WebSocket."""
        await self.send(text_data=json.dumps(event))
    
    async def typing_indicator(self, event):
        """Send typing indicator to WebSocket."""
        # Don't send typing indicator to the user who is typing
        if event['user_id'] != str(self.user.id):
            await self.send(text_data=json.dumps(event))
    
    async def user_joined(self, event):
        """Send user joined notification to WebSocket."""
        if event['user_id'] != str(self.user.id):
            await self.send(text_data=json.dumps(event))
    
    async def user_left(self, event):
        """Send user left notification to WebSocket."""
        if event['user_id'] != str(self.user.id):
            await self.send(text_data=json.dumps(event))
    
    async def user_status_update(self, event):
        """Send user status update to WebSocket."""
        await self.send(text_data=json.dumps(event))
    
    @database_sync_to_async
    def check_channel_access(self):
        """Check if user can access the channel."""
        try:
            channel = Channel.objects.get(id=self.channel_id, is_active=True)
            return channel.can_user_access(self.user)
        except Channel.DoesNotExist:
            return False
    
    @database_sync_to_async
    def mark_message_as_read(self, message_id):
        """Mark message as read for the user."""
        try:
            message = Message.objects.get(id=message_id, is_deleted=False)
            message_status, created = MessageStatus.objects.get_or_create(
                message=message,
                user=self.user,
                defaults={'status': 'read', 'read_at': timezone.now()}
            )
            if not created and message_status.status != 'read':
                message_status.mark_as_read()
            return True
        except Message.DoesNotExist:
            return False


class UserConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for user-specific notifications and updates."""
    
    async def connect(self):
        self.user_id = self.scope['url_route']['kwargs']['user_id']
        self.user = self.scope['user']
        
        # Check if user is authenticated and accessing their own connection
        if not self.user.is_authenticated or str(self.user.id) != self.user_id:
            await self.close()
            return
        
        self.user_group_name = f'user_{self.user_id}'
        
        # Join user group
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Update user online status
        await self.update_user_online_status(True)
    
    async def disconnect(self, close_code):
        # Update user online status
        await self.update_user_online_status(False)
        
        # Leave user group
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )
    
    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'status_update':
                await self.handle_status_update(data.get('status'), data.get('status_message'))
            elif message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': timezone.now().isoformat()
                }))
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON'
            }))
    
    async def handle_status_update(self, status, status_message):
        """Handle user status update."""
        await self.update_user_status(status, status_message)
    
    # Event handlers for user-specific messages
    async def notification(self, event):
        """Send notification to user."""
        await self.send(text_data=json.dumps(event))
    
    async def direct_message(self, event):
        """Send direct message notification to user."""
        await self.send(text_data=json.dumps(event))
    
    async def mention(self, event):
        """Send mention notification to user."""
        await self.send(text_data=json.dumps(event))
    
    @database_sync_to_async
    def update_user_online_status(self, is_online):
        """Update user's online status."""
        try:
            self.user.set_online_status(is_online)
            return True
        except:
            return False
    
    @database_sync_to_async
    def update_user_status(self, status, status_message):
        """Update user's status and status message."""
        try:
            if status:
                self.user.status = status
            if status_message is not None:
                self.user.status_message = status_message
            self.user.save(update_fields=['status', 'status_message'])
            return True
        except:
            return False
