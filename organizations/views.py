from django.contrib.auth.hashers import make_password
from django.template.defaultfilters import first
from rest_framework import status, generics, permissions, serializers
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db.models import Q, Count
from drf_spectacular.utils import extend_schema, OpenApiParameter

from mycorrmessaging.modules.email_messages import send_organization_invitation_email
from mycorrmessaging.modules.exceptions import raise_serializer_error_msg, NotOrganisationOwnerOrAdminException, \
    NotOrganisationMemberException
from mycorrmessaging.modules.paginations import CustomPagination
from mycorrmessaging.settings.base import FRONTEND_URL
from .models import Organization, OrganizationMembership, OrganizationInvitation
from .serializers import (
    OrganizationCreateSerializerIn, OrganizationUpdateSerializerIn,
    OrganizationSerializerOut, OrganizationListSerializerOut,
    OrganizationMembershipSerializerOut, OrganizationMembershipUpdateSerializerIn,
    OrganizationInvitationCreateSerializerIn, OrganizationInvitationSerializerOut,
    OrganizationBulkInvitationSerializerOut, OrganizationInvitationAcceptSerializerIn,
    OrganizationStatsSerializerOut, LeaveOrganizationSerializerOut, DeleteResponseSerializerOut
)

User = get_user_model()


class OrganizationCreateAPIView(APIView):
    """API view for creating organizations."""
    permission_classes = []

    @extend_schema(
        request=OrganizationCreateSerializerIn,
        responses={status.HTTP_201_CREATED: OrganizationSerializerOut}
    )
    def post(self, request):
        serializer = OrganizationCreateSerializerIn(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        response = serializer.save()
        # response_data = OrganizationSerializerOut(organization, context={'request': request}).data
        # response_data['message'] = 'Organization created successfully'

        return Response(response, status=status.HTTP_201_CREATED)


class OrganizationListAPIView(generics.ListAPIView):
    """API view for listing organizations."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = OrganizationListSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        user = self.request.user
        # Return organizations where user is a member
        return Organization.objects.filter(
            memberships__user=user,
            memberships__status='active',
            is_active=True
        ).distinct()


class OrganizationDetailAPIView(APIView):
    """API view for organization details."""
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self, slug):
        organization = get_object_or_404(Organization, slug=slug, is_active=True)
        if not organization.is_member(self.request.user):
            raise NotOrganisationMemberException
        return organization

    @extend_schema(responses={status.HTTP_200_OK: OrganizationSerializerOut})
    def get(self, request, slug):
        """Get organization details."""
        organization = self.get_object(slug)
        serializer = OrganizationSerializerOut(
            organization,
            context={'request': request}
        )
        return Response(serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        request=OrganizationUpdateSerializerIn,
        responses={status.HTTP_200_OK: OrganizationSerializerOut}
    )
    def patch(self, request, slug):
        """Update organization details."""
        organization = self.get_object(slug)

        # Check if user is admin
        if not organization.is_admin(request.user):
            raise NotOrganisationOwnerOrAdminException

        serializer = OrganizationUpdateSerializerIn(
            organization,
            data=request.data,
            partial=True
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        organization = serializer.save()
        response_data = OrganizationSerializerOut(
            organization,
            context={'request': request}
        ).data
        response_data['message'] = 'Organization updated successfully'

        return Response(response_data, status=status.HTTP_200_OK)

    @extend_schema(responses={status.HTTP_204_NO_CONTENT: DeleteResponseSerializerOut})
    def delete(self, request, slug):
        """Delete organization."""
        organization = self.get_object(slug)

        # Only owner can delete organization
        membership = organization.memberships.get(user=request.user, status='active')
        if membership.role != 'owner':
            raise NotOrganisationOwnerOrAdminException

        organization.is_active = False
        organization.save()

        return Response(
            {'message': 'Organization deleted successfully'},
            status=status.HTTP_204_NO_CONTENT
        )

@extend_schema(
    parameters=[
        OpenApiParameter(name="search", type=str, description="Search email and invite token"),
        OpenApiParameter(name="status", type=str, description="Filter by invitation status"),
        OpenApiParameter(name="date_joined_from", type=str, description="Format: 'YYYY-MM-DD'"),
        OpenApiParameter(name="date_joined_to", type=str, description="Format: 'YYYY-MM-DD'"),
    ],
    responses={status.HTTP_200_OK: OrganizationMembershipSerializerOut}
)
class OrganizationMembersAPIView(generics.ListAPIView):
    """API view for listing organization members."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = OrganizationMembershipSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        slug = self.kwargs['slug']
        organization = get_object_or_404(Organization, slug=slug, is_active=True)

        if not organization.is_member(self.request.user):
            raise NotOrganisationMemberException

        admin_user = organization.is_admin(self.request.user)

        if admin_user:
            query = Q()
        else:
            query = Q(status='active')

        request = self.request
        search = request.GET.get("search")
        member_status = request.GET.get("status")
        date_joined_from = request.GET.get("date_joined_from")
        date_joined_to = request.GET.get("date_joined_to")

        if search:
            query &= Q(user__first_name__icontains=search) | Q(user__last_name__icontains=search) | Q(user__email__icontains=search)
        if member_status and admin_user:
            query &= Q(status=member_status)
        if date_joined_from and date_joined_to:
            query &= Q(created_at__range=[date_joined_from, date_joined_to])

        return organization.memberships.filter(query).select_related('user', 'invited_by')


class OrganizationMemberDetailAPIView(APIView):
    """API view for managing organization member details."""
    permission_classes = [permissions.IsAuthenticated]

    def get_objects(self, slug, user_id):
        organization = get_object_or_404(Organization, slug=slug, is_active=True)
        if not organization.is_member(self.request.user):
            raise NotOrganisationMemberException

        membership = get_object_or_404(
            OrganizationMembership,
            organization=organization,
            user_id=user_id,
            status='active'
        )
        return organization, membership

    @extend_schema(
        request=OrganizationMembershipUpdateSerializerIn,
        responses={status.HTTP_200_OK: OrganizationMembershipSerializerOut}
    )
    def patch(self, request, slug, user_id):
        """Update member role or status."""
        organization, membership = self.get_objects(slug, user_id)

        # Check if user is admin
        if not organization.is_admin(request.user):
            raise NotOrganisationOwnerOrAdminException

        # Prevent changing owner role
        if membership.role == 'owner':
            raise NotOrganisationOwnerOrAdminException

        serializer = OrganizationMembershipUpdateSerializerIn(
            membership,
            data=request.data,
            partial=True
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        membership = serializer.save()
        response_data = OrganizationMembershipSerializerOut(membership).data
        response_data['message'] = 'Member updated successfully'

        return Response(response_data, status=status.HTTP_200_OK)

    @extend_schema(responses={status.HTTP_204_NO_CONTENT: DeleteResponseSerializerOut})
    def delete(self, request, slug, user_id):
        """Remove member from organization."""
        organization, membership = self.get_objects(slug, user_id)

        # Check permissions
        if membership.user == request.user:
            # User can remove themselves (leave organization)
            if membership.role == 'owner':
                raise NotOrganisationOwnerOrAdminException
        else:
            # Admin can remove other members
            if not organization.is_admin(request.user):
                raise NotOrganisationOwnerOrAdminException
            if membership.role == 'owner':
                raise NotOrganisationOwnerOrAdminException

        membership.status = 'inactive'
        membership.save()

        return Response(
            {'message': 'Member removed successfully'},
            status=status.HTTP_204_NO_CONTENT
        )


class OrganizationInvitationsAPIView(APIView, CustomPagination):
    """API view for managing organization invitations."""
    permission_classes = [permissions.IsAuthenticated]

    def get_organization(self, slug):
        organization = get_object_or_404(Organization, slug=slug, is_active=True)
        if not organization.is_admin(self.request.user):
            raise NotOrganisationOwnerOrAdminException
        return organization

    @extend_schema(
        parameters=[
            OpenApiParameter(name="search", type=str, description="Search email and invite token"),
            OpenApiParameter(name="status", type=str, description="Filter by invitation status"),
            OpenApiParameter(name="date_created_from", type=str, description="Format: 'YYYY-MM-DD'"),
            OpenApiParameter(name="date_created_to", type=str, description="Format: 'YYYY-MM-DD'"),
            OpenApiParameter(name="date_expired_from", type=str, description="Format: 'YYYY-MM-DD'"),
            OpenApiParameter(name="date_expired_to", type=str, description="Format: 'YYYY-MM-DD'"),
        ],
        responses={status.HTTP_200_OK: OrganizationInvitationSerializerOut}
    )
    def get(self, request, slug):
        """List organization invitations."""

        search = request.GET.get("search")
        invite_status = request.GET.get("status")
        date_created_from = request.GET.get("date_created_from")
        date_created_to = request.GET.get("date_created_to")
        date_expired_from = request.GET.get("date_expired_from")
        date_expired_to = request.GET.get("date_expired_to")

        query = Q()
        if search:
            query &= Q(email__icontains=search) | Q(token__icontains=search)
        if invite_status:
            query &= Q(status=invite_status)
        if date_created_from and date_created_to:
            query &= Q(created_at__range=[date_created_from, date_created_to])
        if date_expired_from and date_expired_to:
            query &= Q(expired_at__range=[date_expired_from, date_expired_to])

        organization = self.get_organization(slug)
        invitations = organization.invitations.filter(query)
        queryset = self.paginate_queryset(invitations, request)
        serializer = OrganizationInvitationSerializerOut(queryset, many=True).data
        response = self.get_paginated_response(serializer).data
        return Response(response, status=status.HTTP_200_OK)

    @extend_schema(
        request=OrganizationInvitationCreateSerializerIn,
        responses={status.HTTP_201_CREATED: OrganizationBulkInvitationSerializerOut}
    )
    def post(self, request, slug):
        """Send organization invitations to multiple email addresses."""
        organization = self.get_organization(slug)

        serializer = OrganizationInvitationCreateSerializerIn(
            data=request.data,
            context={'request': request, 'organization': organization}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        # The serializer now returns a list of invitations
        invitations = serializer.save()

        # Prepare response data
        invitations_data = OrganizationInvitationSerializerOut(invitations, many=True).data

        response_data = {
            'message': f'Successfully sent {len(invitations)} invitation(s)',
            'invitations_sent': len(invitations),
            'invitations': invitations_data
        }

        # Send email notifications for each invitation
        for invitation in invitations:
            send_organization_invitation_email(recipient_email=invitation.email, organization_name=str(organization.name).upper(),
                                               invitation_url=f"{FRONTEND_URL}/invite/{invitation.token}")

        return Response(response_data, status=status.HTTP_201_CREATED)


class OrganizationInvitationDetailAPIView(APIView):
    """API view for managing specific organization invitations."""
    permission_classes = [permissions.IsAuthenticated]

    def get_objects(self, slug, invitation_id):
        organization = get_object_or_404(Organization, slug=slug, is_active=True)
        if not organization.is_admin(self.request.user):
            raise NotOrganisationOwnerOrAdminException

        invitation = get_object_or_404(
            OrganizationInvitation,
            id=invitation_id,
            organization=organization
        )
        return organization, invitation

    @extend_schema(responses={status.HTTP_204_NO_CONTENT: DeleteResponseSerializerOut})
    def delete(self, request, slug, invitation_id):
        """Cancel organization invitation."""
        organization, invitation = self.get_objects(slug, invitation_id)

        invitation.status = 'expired'
        invitation.save()

        return Response(
            {'message': 'Invitation cancelled successfully'},
            status=status.HTTP_204_NO_CONTENT
        )


class AcceptOrganizationInvitationAPIView(APIView):
    """API view for accepting organization invitations."""
    permission_classes = []

    @extend_schema(
        request=OrganizationInvitationAcceptSerializerIn,
        responses={status.HTTP_200_OK: OrganizationSerializerOut}
    )
    def post(self, request):
        """Accept organization invitation."""
        serializer = OrganizationInvitationAcceptSerializerIn(data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response, status=status.HTTP_200_OK)


class OrganizationStatsAPIView(APIView):
    """API view for organization statistics."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: OrganizationStatsSerializerOut})
    def get(self, request, slug):
        """Get organization statistics."""
        organization = get_object_or_404(Organization, slug=slug, is_active=True)

        if not organization.is_member(request.user):
            raise NotOrganisationMemberException

        # Calculate statistics
        stats = {
            'total_members': organization.memberships.count(),
            'active_members': organization.memberships.filter(status='active').count(),
            'pending_invitations': organization.invitations.filter(status='pending').count(),
            'total_channels': 0,  # Will be updated when channels are implemented
            'public_channels': 0,
            'private_channels': 0,
        }

        serializer = OrganizationStatsSerializerOut(stats)
        return Response(serializer.data, status=status.HTTP_200_OK)


class LeaveOrganizationAPIView(APIView):
    """API view for leaving an organization."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = LeaveOrganizationSerializerOut

    @extend_schema(responses={
        status.HTTP_200_OK: LeaveOrganizationSerializerOut,
        status.HTTP_400_BAD_REQUEST: serializers.Serializer
    })
    def post(self, request, slug):
        """Leave an organization."""
        organization = get_object_or_404(Organization, slug=slug, is_active=True)

        try:
            membership = organization.memberships.get(user=request.user, status='active')
        except OrganizationMembership.DoesNotExist:
            return Response(
                {'error': 'You are not a member of this organization'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if membership.role == 'owner':
            return Response(
                {'error': 'Owner cannot leave organization. Transfer ownership first.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        membership.status = 'inactive'
        membership.save()

        return Response(
            {'message': 'Left organization successfully'},
            status=status.HTTP_200_OK
        )


class OrganisationInvitationPreviewAPIView(APIView):
    permission_classes = []

    def get(self, request, token):
        try:
            invite = OrganizationInvitation.objects.get(token=token)
        except OrganizationInvitation.DoesNotExist:
            return Response({"message": "Invitation not found"}, status=status.HTTP_404_NOT_FOUND)

        if invite.status == "expired":
            return Response({"message": "This invitation is already cancelled or expired"})

        return Response({"message": "Invitation retrieved", "data": OrganizationInvitationSerializerOut(invite).data})


