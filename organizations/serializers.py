import datetime

from django.conf import settings
from django.contrib.auth.password_validation import validate_password
from django.contrib.auth.hashers import make_password
from django.utils import timezone
from rest_framework import serializers
from django.contrib.auth import get_user_model

from mycorrmessaging.modules.exceptions import InvalidRequestException
from mycorrmessaging.modules.utils import generate_random_password, encrypt_text, get_next_minute, generate_random_otp, \
    generate_random_string
from .models import Organization, OrganizationMembership, OrganizationInvitation

User = get_user_model()


class OrganizationCreateSerializerInOld(serializers.ModelSerializer):
    """Serializer for creating organizations."""
    class Meta:
        model = Organization
        fields = [
            'name', 'description', 'logo', 'website', 'max_members',
            'allow_public_channels', 'require_invitation', 'allow_guest_access'
        ]
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class OrganizationCreateSerializerIn(serializers.Serializer):
    name = serializers.CharField()
    description = serializers.CharField(required=False, allow_blank=True)
    logo = serializers.CharField(required=False, allow_blank=True)
    website = serializers.URLField(required=False, allow_blank=True)
    user_email = serializers.EmailField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()

    def create(self, validated_data):
        name = validated_data.get('name')
        description = validated_data.get('description')
        logo = validated_data.get('logo')
        website = validated_data.get('website')
        user_email = str(validated_data.get('user_email')).lower()
        first_name = validated_data.get('first_name')
        last_name = validated_data.get('last_name')

        # Check if organisation with the same name does not exist
        if Organization.objects.filter(name__iexact=name).exists():
            raise InvalidRequestException({"message": "Organization with this name already exists."})

        # Check if user with the email exists
        if User.objects.filter(email=user_email).exists():
            raise InvalidRequestException({"message": "User with this email already exists."})

        # Generate random password
        password = generate_random_password(10)

        # Generate verification token for email verification
        verification_token = generate_random_otp(6)


        # Create user
        user = User.objects.create(email=user_email, first_name=first_name, last_name=last_name, password=make_password(password),
                                   username=user_email)

        # Create Organization
        Organization.objects.create(name=name, description=description, logo=logo, website=website, created_by=user)

        detail = {"message": "Account created successfully, you will receive an email containing your password shortly."}

        user.email_verification_token = encrypt_text(verification_token)
        user.email_verification_token_expiry = get_next_minute(datetime.datetime.now(), 15)
        user.save()


        if settings.DEBUG:
            detail['password'] = password
            detail['verification_token'] = verification_token

        return detail


class OrganizationUpdateSerializerIn(serializers.ModelSerializer):
    """Serializer for updating organizations."""
    class Meta:
        model = Organization
        fields = [
            'name', 'description', 'logo', 'website', 'max_members',
            'allow_public_channels', 'require_invitation', 'allow_guest_access'
        ]


class OrganizationSerializerOut(serializers.ModelSerializer):
    """Serializer for organization output."""
    member_count = serializers.ReadOnlyField()
    is_at_capacity = serializers.ReadOnlyField()
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    user_role = serializers.SerializerMethodField()
    
    class Meta:
        model = Organization
        fields = [
            'id', 'name', 'slug', 'description', 'logo', 'website',
            'is_active', 'max_members', 'member_count', 'is_at_capacity',
            'allow_public_channels', 'require_invitation', 'allow_guest_access',
            'created_by', 'created_by_name', 'user_role', 'created_at', 'updated_at'
        ]
    
    def get_user_role(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.get_member_role(request.user)
        return None


class OrganizationListSerializerOut(serializers.ModelSerializer):
    """Serializer for organization list output."""
    member_count = serializers.ReadOnlyField()
    user_role = serializers.SerializerMethodField()
    
    class Meta:
        model = Organization
        fields = [
            'id', 'name', 'slug', 'description', 'logo',
            'member_count', 'user_role', 'created_at'
        ]
    
    def get_user_role(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.get_member_role(request.user)
        return None


class OrganizationMembershipSerializerOut(serializers.ModelSerializer):
    """Serializer for organization membership output."""
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_avatar = serializers.CharField(source='user.avatar', read_only=True)
    invited_by_name = serializers.CharField(source='invited_by.get_full_name', read_only=True)
    invited_by_email = serializers.CharField(source='invited_by.email', read_only=True)
    date_invited = serializers.SerializerMethodField()

    def get_date_invited(self, obj):
        invite = OrganizationInvitation.objects.filter(email=obj.user.email, status='accepted').last()
        return invite.created_at if invite else None

    class Meta:
        model = OrganizationMembership
        fields = [
            'id', 'user', 'user_name', 'user_email', 'user_avatar', 'date_invited',
            'role', 'status', 'joined_at', 'invited_by', 'invited_by_name', 'invited_by_email'
        ]


class OrganizationMembershipUpdateSerializerIn(serializers.ModelSerializer):
    """Serializer for updating organization membership."""
    class Meta:
        model = OrganizationMembership
        fields = ['role', 'status']


class OrganizationInvitationCreateSerializerIn(serializers.Serializer):
    """Serializer for creating organization invitations to multiple email addresses."""
    emails = serializers.ListField(
        child=serializers.EmailField(),
        min_length=1,
        help_text="List of email addresses to invite to the organization"
    )
    role = serializers.ChoiceField(
        choices=['member', 'admin'],
        default='member',
        help_text="Role to assign to invited users"
    )
    message = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text="Optional message to include with the invitation"
    )

    def validate_emails(self, value):
        """Validate the list of email addresses."""
        organization = self.context['organization']

        # Remove duplicates while preserving order
        seen = set()
        unique_emails = []
        for email in value:
            email_lower = email.lower()
            if email_lower not in seen:
                seen.add(email_lower)
                unique_emails.append(email)

        # Check if duplicates were removed
        if len(unique_emails) != len(value):
            raise InvalidRequestException({"message": "Duplicate email addresses are not allowed."})

        # Validate each email address
        errors = list()
        valid_emails = list()

        for i, email in enumerate(unique_emails):
            try:
                # Check if user is already a member
                if User.objects.filter(email=email).exists():
                    user = User.objects.get(email=email)
                    if organization.is_member(user):
                        errors.append(f"User with email {email} is already a member of this organization.")
                        continue

                # Check if there's already a pending invitation
                if OrganizationInvitation.objects.filter(
                    organization=organization,
                    email=email,
                    status='pending'
                ).exists():
                    errors.append(f"There is already a pending invitation for {email}.")
                    continue

                valid_emails.append(email)

            except Exception as e:
                errors.append(f"Error validating {email}: {str(e)}")

        # If there are validation errors, raise them
        if errors:
            raise InvalidRequestException({"message": errors[0]})

        # Ensure at least one valid email remains
        if not valid_emails:
            raise InvalidRequestException({"message": "No valid email addresses provided."})

        return valid_emails

    def validate(self, attrs):
        """Additional validation for the entire serializer."""
        organization = self.context['organization']
        emails = attrs.get('emails', [])

        # Check if organization would exceed capacity
        current_members = organization.memberships.filter(status='active').count()
        pending_invitations = organization.invitations.filter(status='pending').count()
        total_after_invites = current_members + pending_invitations + len(emails)

        if hasattr(organization, 'max_members') and organization.max_members:
            if total_after_invites > organization.max_members:
                raise InvalidRequestException({"message":
                    f"Adding {len(emails)} invitations would exceed the organization's "
                    f"maximum capacity of {organization.max_members} members."})

        return attrs

    def create(self, validated_data):
        """Create multiple invitation objects."""
        organization = self.context['organization']
        invited_by = self.context['request'].user
        emails = validated_data['emails']
        role = validated_data.get('role', 'member')
        message = validated_data.get('message', '')

        while True:
            random_token = generate_random_string(10)
            if not OrganizationInvitation.objects.filter(token=random_token).exists():
                break

        invitations = []
        for email in emails:
            invitation = OrganizationInvitation.objects.create(
                organization=organization,
                email=email,
                role=role,
                message=message,
                invited_by=invited_by,
                token=random_token,
            )
            invitations.append(invitation)

        # Return the list of created invitations
        # Note: This changes the return type from a single object to a list
        return invitations


class OrganizationInvitationSerializerOut(serializers.ModelSerializer):
    """Serializer for organization invitation output."""
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    organization_logo = serializers.CharField(source='organization.logo', read_only=True)
    invited_by_name = serializers.CharField(source='invited_by.get_full_name', read_only=True)
    is_expired = serializers.ReadOnlyField()
    is_valid = serializers.ReadOnlyField()
    
    class Meta:
        model = OrganizationInvitation
        fields = [
            'id', 'organization', 'organization_name', 'organization_logo',
            'email', 'role', 'status', 'token', 'expires_at', 'accepted_at',
            'message', 'invited_by', 'invited_by_name', 'is_expired', 'is_valid',
            'created_at'
        ]


class OrganizationBulkInvitationSerializerOut(serializers.Serializer):
    """Serializer for bulk organization invitation response."""
    message = serializers.CharField(help_text="Success message")
    invitations_sent = serializers.IntegerField(help_text="Number of invitations sent")
    invitations = OrganizationInvitationSerializerOut(many=True, help_text="List of created invitations")


class OrganizationInvitationAcceptSerializerIn(serializers.Serializer):
    """Serializer for accepting organization invitations."""
    token = serializers.CharField()
    first_name = serializers.CharField()
    last_name = serializers.CharField()
    password = serializers.CharField(write_only=True, validators=[validate_password])

    def create(self, validated_data):
        """Accept the invitation and create a new user."""
        token_value = validated_data.get("token")
        first_name = validated_data.get("first_name")
        last_name = validated_data.get("last_name")
        password = validated_data.get("password")

        try:
            invitation = OrganizationInvitation.objects.get(token=token_value)
            if not invitation.is_valid:
                raise InvalidRequestException({"message": "Invalid or expired invitation."})
        except OrganizationInvitation.DoesNotExist:
            raise InvalidRequestException({"message": "Invalid invitation token."})

        user_email = invitation.email
        check_user = User.objects.filter(email__iexact=user_email)

        # Check if user is already a member
        if check_user.exists():
            existing_user = check_user.first()
            if invitation.organization.is_member(existing_user):
                raise InvalidRequestException({'message': 'You are already a member of this organization'})
            raise InvalidRequestException({'message': 'User with this email already exists'})

        # Check if organization is at capacity
        if invitation.organization.is_at_capacity:
            raise InvalidRequestException({'message': 'Organization is at maximum capacity'})

        # Create new user
        new_user = User.objects.create(
            email=user_email, first_name=first_name, last_name=last_name, password=make_password(password), password_changed=True,
            username=user_email
        )

        # Create membership
        OrganizationMembership.objects.create(
            organization=invitation.organization,
            user=new_user,
            role=invitation.role,
            status='active',
            invited_by=invitation.invited_by
        )

        # Update invitation status
        invitation.status = 'accepted'
        invitation.accepted_at = timezone.now()
        invitation.save()

        response_data = OrganizationSerializerOut(invitation.organization, context={'request': self.context.get('request')}).data

        return {"message": "Invitation accepted successfully", "data": response_data}



class OrganizationStatsSerializerOut(serializers.Serializer):
    """Serializer for organization statistics."""
    total_members = serializers.IntegerField()
    active_members = serializers.IntegerField()
    pending_invitations = serializers.IntegerField()
    total_channels = serializers.IntegerField()
    public_channels = serializers.IntegerField()
    private_channels = serializers.IntegerField()


# Response serializers for function-based views
class LeaveOrganizationSerializerOut(serializers.Serializer):
    """Serializer for leave organization response."""
    message = serializers.CharField(help_text="Success message")


class DeleteResponseSerializerOut(serializers.Serializer):
    """Generic serializer for delete responses."""
    message = serializers.CharField(help_text="Success message")
