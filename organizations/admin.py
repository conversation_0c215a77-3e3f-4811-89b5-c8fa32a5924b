from django.contrib import admin
from django.utils.html import format_html
from .models import Organization, OrganizationMembership, OrganizationInvitation


class OrganizationMembershipInline(admin.TabularInline):
    """Inline admin for organization memberships."""
    model = OrganizationMembership
    extra = 0
    readonly_fields = ['joined_at']
    fields = ['user', 'role', 'status', 'joined_at', 'invited_by']


class OrganizationInvitationInline(admin.TabularInline):
    """Inline admin for organization invitations."""
    model = OrganizationInvitation
    extra = 0
    readonly_fields = ['token', 'created_at', 'expires_at', 'is_expired']
    fields = ['email', 'role', 'status', 'invited_by', 'expires_at', 'is_expired']


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    """Admin configuration for Organization model."""

    list_display = [
        'name', 'slug', 'member_count', 'is_active', 'created_by', 'created_at'
    ]
    list_filter = [
        'is_active', 'allow_public_channels', 'require_invitation',
        'allow_guest_access', 'created_at'
    ]
    search_fields = ['name', 'slug', 'description', 'created_by__email']
    readonly_fields = ['id', 'slug', 'member_count', 'is_at_capacity', 'created_at', 'updated_at']
    prepopulated_fields = {'slug': ('name',)}
    inlines = [OrganizationMembershipInline, OrganizationInvitationInline]

    fieldsets = (
        (None, {
            'fields': ('name', 'slug', 'description', 'logo', 'website')
        }),
        ('Settings', {
            'fields': (
                'is_active', 'max_members', 'allow_public_channels',
                'require_invitation', 'allow_guest_access'
            )
        }),
        ('Statistics', {
            'fields': ('member_count', 'is_at_capacity')
        }),
        ('Metadata', {
            'fields': ('created_by', 'created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('created_by')


@admin.register(OrganizationMembership)
class OrganizationMembershipAdmin(admin.ModelAdmin):
    """Admin configuration for OrganizationMembership model."""

    list_display = [
        'user', 'organization', 'role', 'status', 'joined_at'
    ]
    list_filter = ['role', 'status', 'joined_at']
    search_fields = [
        'user__email', 'user__first_name', 'user__last_name',
        'organization__name'
    ]
    readonly_fields = ['id', 'joined_at', 'created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('organization', 'user', 'role', 'status')
        }),
        ('Invitation Info', {
            'fields': ('invited_by',)
        }),
        ('Timestamps', {
            'fields': ('joined_at', 'created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'organization', 'invited_by'
        )


@admin.register(OrganizationInvitation)
class OrganizationInvitationAdmin(admin.ModelAdmin):
    """Admin configuration for OrganizationInvitation model."""

    list_display = [
        'email', 'organization', 'role', 'status', 'invited_by',
        'is_expired', 'created_at'
    ]
    list_filter = ['role', 'status', 'created_at', 'expires_at']
    search_fields = [
        'email', 'organization__name', 'invited_by__email'
    ]
    readonly_fields = [
        'id', 'token', 'is_expired', 'is_valid', 'created_at', 'updated_at'
    ]

    fieldsets = (
        (None, {
            'fields': ('organization', 'email', 'role', 'status')
        }),
        ('Invitation Details', {
            'fields': ('token', 'message', 'invited_by')
        }),
        ('Timing', {
            'fields': ('expires_at', 'accepted_at', 'is_expired', 'is_valid')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'organization', 'invited_by'
        )
