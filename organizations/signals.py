import uuid
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils.text import slugify
from .models import Organization, OrganizationMembership, OrganizationInvitation


@receiver(post_save, sender=Organization)
def create_organization_owner_membership(sender, instance, created, **kwargs):
    """
    Create owner membership for the user who created the organization.
    """
    if created:
        OrganizationMembership.objects.create(
            organization=instance,
            user=instance.created_by,
            role='owner',
            status='active'
        )


@receiver(post_save, sender=Organization)
def generate_organization_slug(sender, instance, created, **kwargs):
    """
    Generate a unique slug for the organization if not provided.
    """
    if created and not instance.slug:
        base_slug = slugify(instance.name)
        slug = base_slug
        counter = 1
        
        while Organization.objects.filter(slug=slug).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1
        
        instance.slug = slug
        instance.save(update_fields=['slug'])


@receiver(post_save, sender=OrganizationInvitation)
def generate_invitation_token(sender, instance, created, **kwargs):
    """
    Generate a unique token for the invitation if not provided.
    """
    if created and not instance.token:
        instance.token = str(uuid.uuid4())
        instance.save(update_fields=['token'])
