from django.db import models
from django.conf import settings
from django.utils import timezone
from datetime import timedelta
from home.models import BaseModel
from mycorrmessaging.modules.choices import (
    ORGANIZATION_ROLE_CHOICES, ORGANIZATION_MEMBER_STATUS_CHOICES,
    INVITATION_STATUS_CHOICES
)


class Organization(BaseModel):
    """
    Model representing an organization/workspace in the messaging platform.
    """
    name = models.CharField(
        max_length=255,
        help_text="Organization name"
    )
    slug = models.SlugField(
        max_length=255,
        unique=True,
        help_text="URL-friendly organization identifier"
    )
    description = models.TextField(
        null=True,
        blank=True,
        help_text="Organization description"
    )
    logo = models.TextField(
        null=True,
        blank=True,
        help_text="URL to organization logo stored in cloud storage"
    )
    website = models.URLField(
        null=True,
        blank=True,
        help_text="Organization website"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether organization is active"
    )
    max_members = models.PositiveIntegerField(
        default=100,
        help_text="Maximum number of members allowed"
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_organizations',
        help_text="User who created this organization"
    )

    # Settings
    allow_public_channels = models.BooleanField(
        default=True,
        help_text="Allow creation of public channels"
    )
    require_invitation = models.BooleanField(
        default=True,
        help_text="Require invitation to join organization"
    )
    allow_guest_access = models.BooleanField(
        default=False,
        help_text="Allow guest users to join"
    )

    class Meta:
        db_table = 'organizations_organization'
        verbose_name = 'Organization'
        verbose_name_plural = 'Organizations'
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def member_count(self):
        """Get the number of active members in the organization."""
        return self.memberships.filter(status='active').count()

    @property
    def is_at_capacity(self):
        """Check if organization is at maximum capacity."""
        return self.member_count >= self.max_members

    def get_member_role(self, user):
        """Get the role of a user in this organization."""
        try:
            membership = self.memberships.get(user=user, status='active')
            return membership.role
        except OrganizationMembership.DoesNotExist:
            return None

    def is_member(self, user):
        """Check if user is a member of this organization."""
        return self.memberships.filter(user=user, status='active').exists()

    def is_admin(self, user):
        """Check if user is an admin of this organization."""
        return self.memberships.filter(
            user=user,
            status='active',
            role__in=['owner', 'admin']
        ).exists()


class OrganizationMembership(BaseModel):
    """
    Model representing a user's membership in an organization.
    """
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='memberships',
        help_text="Organization"
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='organization_memberships',
        help_text="User"
    )
    role = models.CharField(
        max_length=20,
        choices=ORGANIZATION_ROLE_CHOICES,
        default='member',
        help_text="User's role in the organization"
    )
    status = models.CharField(
        max_length=20,
        choices=ORGANIZATION_MEMBER_STATUS_CHOICES,
        default='active',
        help_text="Membership status"
    )
    joined_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When user joined the organization"
    )
    invited_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sent_organization_invitations',
        help_text="User who invited this member"
    )

    class Meta:
        db_table = 'organizations_membership'
        verbose_name = 'Organization Membership'
        verbose_name_plural = 'Organization Memberships'
        unique_together = ['organization', 'user']
        ordering = ['-joined_at']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.organization.name} ({self.role})"


class OrganizationInvitation(BaseModel):
    """
    Model representing invitations to join an organization.
    """
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='invitations',
        help_text="Organization"
    )
    email = models.EmailField(
        help_text="Email address of the invitee"
    )
    invited_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='sent_invitations',
        help_text="User who sent the invitation"
    )
    role = models.CharField(
        max_length=20,
        choices=ORGANIZATION_ROLE_CHOICES,
        default='member',
        help_text="Role to assign to the invitee"
    )
    status = models.CharField(
        max_length=20,
        choices=INVITATION_STATUS_CHOICES,
        default='pending',
        help_text="Invitation status"
    )
    token = models.CharField(
        max_length=255,
        unique=True,
        help_text="Unique invitation token"
    )
    expires_at = models.DateTimeField(
        help_text="When the invitation expires"
    )
    accepted_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the invitation was accepted"
    )
    message = models.TextField(
        null=True,
        blank=True,
        help_text="Optional message from the inviter"
    )

    class Meta:
        db_table = 'organizations_invitation'
        verbose_name = 'Organization Invitation'
        verbose_name_plural = 'Organization Invitations'
        unique_together = ['organization', 'email']
        ordering = ['-created_at']

    def __str__(self):
        return f"Invitation to {self.email} for {self.organization.name}"

    @property
    def is_expired(self):
        """Check if the invitation has expired."""
        return timezone.now() > self.expires_at

    @property
    def is_valid(self):
        """Check if the invitation is valid (pending and not expired)."""
        return self.status == 'pending' and not self.is_expired

    def save(self, *args, **kwargs):
        if not self.expires_at:
            # Set expiration to one hour from now
            self.expires_at = timezone.now() + timedelta(hours=1)
        super().save(*args, **kwargs)
