# Generated by Django 5.2.5 on 2025-08-08 08:34

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Organization name', max_length=255)),
                ('slug', models.SlugField(help_text='URL-friendly organization identifier', max_length=255, unique=True)),
                ('description', models.TextField(blank=True, help_text='Organization description', null=True)),
                ('logo', models.ImageField(blank=True, help_text='Organization logo', null=True, upload_to='organizations/logos/')),
                ('website', models.URLField(blank=True, help_text='Organization website', null=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether organization is active')),
                ('max_members', models.PositiveIntegerField(default=100, help_text='Maximum number of members allowed')),
                ('allow_public_channels', models.BooleanField(default=True, help_text='Allow creation of public channels')),
                ('require_invitation', models.BooleanField(default=True, help_text='Require invitation to join organization')),
                ('allow_guest_access', models.BooleanField(default=False, help_text='Allow guest users to join')),
                ('created_by', models.ForeignKey(help_text='User who created this organization', on_delete=django.db.models.deletion.CASCADE, related_name='created_organizations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Organization',
                'verbose_name_plural': 'Organizations',
                'db_table': 'organizations_organization',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='OrganizationInvitation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('email', models.EmailField(help_text='Email address of the invitee', max_length=254)),
                ('role', models.CharField(choices=[('owner', 'Owner'), ('admin', 'Admin'), ('member', 'Member'), ('guest', 'Guest')], default='member', help_text='Role to assign to the invitee', max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('accepted', 'Accepted'), ('declined', 'Declined'), ('expired', 'Expired')], default='pending', help_text='Invitation status', max_length=20)),
                ('token', models.CharField(help_text='Unique invitation token', max_length=255, unique=True)),
                ('expires_at', models.DateTimeField(help_text='When the invitation expires')),
                ('accepted_at', models.DateTimeField(blank=True, help_text='When the invitation was accepted', null=True)),
                ('message', models.TextField(blank=True, help_text='Optional message from the inviter', null=True)),
                ('invited_by', models.ForeignKey(help_text='User who sent the invitation', on_delete=django.db.models.deletion.CASCADE, related_name='sent_invitations', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(help_text='Organization', on_delete=django.db.models.deletion.CASCADE, related_name='invitations', to='organizations.organization')),
            ],
            options={
                'verbose_name': 'Organization Invitation',
                'verbose_name_plural': 'Organization Invitations',
                'db_table': 'organizations_invitation',
                'ordering': ['-created_at'],
                'unique_together': {('organization', 'email')},
            },
        ),
        migrations.CreateModel(
            name='OrganizationMembership',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('role', models.CharField(choices=[('owner', 'Owner'), ('admin', 'Admin'), ('member', 'Member'), ('guest', 'Guest')], default='member', help_text="User's role in the organization", max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending', 'Pending'), ('suspended', 'Suspended')], default='active', help_text='Membership status', max_length=20)),
                ('joined_at', models.DateTimeField(auto_now_add=True, help_text='When user joined the organization')),
                ('invited_by', models.ForeignKey(blank=True, help_text='User who invited this member', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_organization_invitations', to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(help_text='Organization', on_delete=django.db.models.deletion.CASCADE, related_name='memberships', to='organizations.organization')),
                ('user', models.ForeignKey(help_text='User', on_delete=django.db.models.deletion.CASCADE, related_name='organization_memberships', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Organization Membership',
                'verbose_name_plural': 'Organization Memberships',
                'db_table': 'organizations_membership',
                'ordering': ['-joined_at'],
                'unique_together': {('organization', 'user')},
            },
        ),
    ]
