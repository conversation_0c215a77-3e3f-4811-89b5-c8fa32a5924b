from django.urls import path
from . import views

app_name = 'organizations'

urlpatterns = [
    # Organization management
    path('create/', views.OrganizationCreateAPIView.as_view(), name='create'),
    path('list/', views.OrganizationListAPIView.as_view(), name='list'),
    path('<slug:slug>/', views.OrganizationDetailAPIView.as_view(), name='detail'),
    path('<slug:slug>/stats/', views.OrganizationStatsAPIView.as_view(), name='stats'),
    path('<slug:slug>/leave/', views.LeaveOrganizationAPIView.as_view(), name='leave'),
    
    # Member management
    path('<slug:slug>/members/', views.OrganizationMembersAPIView.as_view(), name='members'),
    path('<slug:slug>/members/<uuid:user_id>/', views.OrganizationMemberDetailAPIView.as_view(), name='member_detail'),
    
    # Invitation management
    path('<slug:slug>/invitations/', views.OrganizationInvitationsAPIView.as_view(), name='invitations'),
    path('<slug:slug>/invitations/<uuid:invitation_id>/', views.OrganizationInvitationDetailAPIView.as_view(), name='invitation_detail'),
    path('invitations/accept/', views.AcceptOrganizationInvitationAPIView.as_view(), name='accept_invitation'),
    path('invitations/preview/<str:token>/', views.OrganisationInvitationPreviewAPIView.as_view(), name='invite_preview'),
]
