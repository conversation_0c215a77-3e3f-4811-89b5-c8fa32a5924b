# MyCorr Messaging Platform - Deployment Guide

## Overview

This guide covers deploying the MyCorr Messaging Platform to production environments. The platform is built with Django and requires several components for full functionality.

## Prerequisites

- Python 3.9+
- PostgreSQL 12+
- Redis 6+
- Nginx (for production)
- SSL certificate (recommended)

## Environment Setup

### 1. Clone Repository

```bash
git clone <repository-url>
cd mycorrmessaging
```

### 2. Create Virtual Environment

```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Environment Variables

Create a `.env` file in the project root:

```env
# Django Settings
DEBUG=False
SECRET_KEY=your-super-secret-key-here
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/mycorrmessaging

# Redis
REDIS_URL=redis://localhost:6379/0

# Email Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# File Storage (AWS S3)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=us-east-1

# Security
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

## Database Setup

### 1. Create PostgreSQL Database

```sql
CREATE DATABASE mycorrmessaging;
CREATE USER mycorr_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE mycorrmessaging TO mycorr_user;
```

### 2. Run Migrations

```bash
python manage.py migrate
```

### 3. Create Superuser

```bash
python manage.py createsuperuser
```

### 4. Collect Static Files

```bash
python manage.py collectstatic --noinput
```

## Redis Setup

### Install Redis

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis

# macOS
brew install redis
```

### Configure Redis

Edit `/etc/redis/redis.conf`:

```
bind 127.0.0.1
port 6379
requirepass your_redis_password
```

Start Redis:

```bash
sudo systemctl start redis
sudo systemctl enable redis
```

## Production Deployment

### 1. Gunicorn Setup

Install Gunicorn:

```bash
pip install gunicorn
```

Create Gunicorn configuration file `gunicorn.conf.py`:

```python
bind = "127.0.0.1:8000"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
```

### 2. Systemd Service

Create `/etc/systemd/system/mycorrmessaging.service`:

```ini
[Unit]
Description=MyCorr Messaging Platform
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/mycorrmessaging
Environment=PATH=/path/to/mycorrmessaging/venv/bin
EnvironmentFile=/path/to/mycorrmessaging/.env
ExecStart=/path/to/mycorrmessaging/venv/bin/gunicorn mycorrmessaging.asgi:application -c gunicorn.conf.py
Restart=always

[Install]
WantedBy=multi-user.target
```

Enable and start the service:

```bash
sudo systemctl daemon-reload
sudo systemctl enable mycorrmessaging
sudo systemctl start mycorrmessaging
```

### 3. Nginx Configuration

Create `/etc/nginx/sites-available/mycorrmessaging`:

```nginx
upstream mycorrmessaging {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    client_max_body_size 100M;

    location / {
        proxy_pass http://mycorrmessaging;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws/ {
        proxy_pass http://mycorrmessaging;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /path/to/mycorrmessaging/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /media/ {
        alias /path/to/mycorrmessaging/media/;
        expires 1y;
        add_header Cache-Control "public";
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/mycorrmessaging /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## Monitoring and Logging

### 1. Application Logging

Configure logging in `settings.py`:

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/mycorrmessaging/django.log',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
}
```

### 2. Health Check Endpoint

Add to `urls.py`:

```python
from django.http import JsonResponse

def health_check(request):
    return JsonResponse({'status': 'healthy'})

urlpatterns = [
    # ... other patterns
    path('health/', health_check, name='health_check'),
]
```

## Backup Strategy

### 1. Database Backup

Create backup script:

```bash
#!/bin/bash
BACKUP_DIR="/backups/mycorrmessaging"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump mycorrmessaging > "$BACKUP_DIR/db_backup_$DATE.sql"
find $BACKUP_DIR -name "db_backup_*.sql" -mtime +7 -delete
```

### 2. Media Files Backup

```bash
#!/bin/bash
BACKUP_DIR="/backups/mycorrmessaging"
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "$BACKUP_DIR/media_backup_$DATE.tar.gz" /path/to/mycorrmessaging/media/
find $BACKUP_DIR -name "media_backup_*.tar.gz" -mtime +7 -delete
```

## Security Checklist

- [ ] Use HTTPS in production
- [ ] Set strong SECRET_KEY
- [ ] Configure CORS properly
- [ ] Enable rate limiting
- [ ] Set up firewall rules
- [ ] Regular security updates
- [ ] Monitor logs for suspicious activity
- [ ] Use environment variables for secrets
- [ ] Enable database connection encryption
- [ ] Configure Redis authentication

## Performance Optimization

### 1. Database Optimization

- Enable connection pooling
- Add database indexes for frequently queried fields
- Use database query optimization

### 2. Caching

- Configure Redis for session storage
- Implement query result caching
- Use CDN for static files

### 3. WebSocket Scaling

For high-traffic deployments, consider:

- Redis as channel layer backend
- Multiple application servers
- Load balancer with sticky sessions

## Troubleshooting

### Common Issues

1. **WebSocket connections failing**
   - Check Nginx WebSocket configuration
   - Verify Redis connectivity
   - Check firewall settings

2. **File uploads not working**
   - Check file permissions
   - Verify storage configuration
   - Check client_max_body_size in Nginx

3. **Database connection errors**
   - Verify database credentials
   - Check database server status
   - Review connection pool settings

### Log Locations

- Application logs: `/var/log/mycorrmessaging/`
- Nginx logs: `/var/log/nginx/`
- System logs: `/var/log/syslog`

## Maintenance

### Regular Tasks

- Update dependencies monthly
- Monitor disk space
- Review and rotate logs
- Test backup restoration
- Security patches
- Performance monitoring
