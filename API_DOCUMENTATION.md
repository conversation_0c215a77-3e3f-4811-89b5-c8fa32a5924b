# MyCorr Messaging Platform API Documentation

## Overview

The MyCorr Messaging Platform is a comprehensive Slack-like communication backend built with Django REST Framework and WebSocket support. This API provides all the necessary endpoints for building a modern team communication application.

## Base URL

```
http://localhost:8000/api/
```

## Authentication

The API uses JWT (JSON Web Token) authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

### Authentication Endpoints

#### Register User
- **POST** `/auth/register/`
- **Body**: `{"email": "<EMAIL>", "username": "username", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "password": "password123", "password_confirm": "password123"}`
- **Response**: User data with JWT tokens

#### Login
- **POST** `/auth/login/`
- **Body**: `{"email": "<EMAIL>", "password": "password123"}`
- **Response**: User data with JWT tokens

#### Logout
- **POST** `/auth/logout/`
- **Body**: `{"refresh_token": "refresh_token_here"}`

#### Refresh Token
- **POST** `/auth/token/refresh/`
- **Body**: `{"refresh": "refresh_token_here"}`

## User Management

### Profile Management
- **GET** `/auth/profile/` - Get current user profile
- **PATCH** `/auth/profile/` - Update user profile
- **PATCH** `/auth/status/` - Update user status
- **GET** `/auth/profile/<user_id>/` - Get specific user profile

### User Preferences
- **GET** `/auth/preferences/` - Get user preferences
- **PATCH** `/auth/preferences/` - Update user preferences

### User Discovery
- **GET** `/auth/search/?q=<query>` - Search users
- **GET** `/auth/list/` - List all users
- **GET** `/auth/online-users/` - Get online users
- **GET** `/auth/activity/<user_id>/` - Get user activity

## Organization Management

### Organizations
- **POST** `/organizations/create/` - Create organization
- **GET** `/organizations/list/` - List user's organizations
- **GET** `/organizations/<slug>/` - Get organization details
- **PATCH** `/organizations/<slug>/` - Update organization
- **DELETE** `/organizations/<slug>/` - Delete organization
- **GET** `/organizations/<slug>/stats/` - Get organization statistics

### Organization Members
- **GET** `/organizations/<slug>/members/` - List organization members
- **PATCH** `/organizations/<slug>/members/<user_id>/` - Update member role
- **DELETE** `/organizations/<slug>/members/<user_id>/` - Remove member
- **POST** `/organizations/<slug>/leave/` - Leave organization

### Organization Invitations
- **GET** `/organizations/<slug>/invitations/` - List pending invitations
- **POST** `/organizations/<slug>/invitations/` - Send invitation
- **DELETE** `/organizations/<slug>/invitations/<invitation_id>/` - Cancel invitation
- **POST** `/organizations/invitations/accept/` - Accept invitation

## Channel Management

### Channels
- **POST** `/channels/create/` - Create channel
- **GET** `/channels/org/<org_slug>/` - List organization channels
- **GET** `/channels/org/<org_slug>/<channel_slug>/` - Get channel details
- **PATCH** `/channels/org/<org_slug>/<channel_slug>/` - Update channel
- **DELETE** `/channels/org/<org_slug>/<channel_slug>/` - Delete channel

### Channel Membership
- **GET** `/channels/org/<org_slug>/<channel_slug>/members/` - List channel members
- **PATCH** `/channels/org/<org_slug>/<channel_slug>/members/<user_id>/` - Update member
- **DELETE** `/channels/org/<org_slug>/<channel_slug>/members/<user_id>/` - Remove member
- **POST** `/channels/org/<org_slug>/<channel_slug>/invite/` - Invite users
- **POST** `/channels/org/<org_slug>/<channel_slug>/join/` - Join channel
- **POST** `/channels/org/<org_slug>/<channel_slug>/leave/` - Leave channel

### Direct Messages
- **POST** `/channels/direct-message/` - Create/get DM channel

## Messaging

### Messages
- **POST** `/messages/send/` - Send message
- **GET** `/messages/channel/<channel_id>/` - List channel messages
- **GET** `/messages/<message_id>/` - Get message details
- **PATCH** `/messages/<message_id>/` - Edit message
- **DELETE** `/messages/<message_id>/` - Delete message
- **GET** `/messages/<message_id>/replies/` - Get message replies

### Message Reactions
- **GET** `/messages/<message_id>/reactions/` - Get message reactions
- **POST** `/messages/<message_id>/reactions/` - Add reaction
- **DELETE** `/messages/<message_id>/reactions/?type=<reaction_type>` - Remove reaction

### Message Status
- **GET** `/messages/<message_id>/status/` - Get message status
- **POST** `/messages/<message_id>/mark-read/` - Mark message as read
- **POST** `/messages/channel/<channel_id>/mark-read/` - Mark all channel messages as read

### File Attachments
- **GET** `/messages/<message_id>/attachments/` - List message attachments
- **POST** `/messages/<message_id>/upload/` - Upload attachment
- **GET** `/messages/attachments/<attachment_id>/` - Get attachment details
- **DELETE** `/messages/attachments/<attachment_id>/` - Delete attachment
- **GET** `/messages/attachments/<attachment_id>/download/` - Download attachment

## Search

### Global Search
- **GET** `/messages/search/global/?q=<query>&type=<all|messages|users|channels>` - Global search
- **GET** `/messages/search/?q=<query>&channel_id=<id>&date_from=<date>` - Search messages
- **GET** `/messages/search/suggestions/` - Get search suggestions

## Notifications

### Notifications
- **GET** `/notifications/` - List notifications
- **GET** `/notifications/<notification_id>/` - Get notification details
- **PATCH** `/notifications/<notification_id>/` - Update notification status
- **DELETE** `/notifications/<notification_id>/` - Delete notification
- **POST** `/notifications/bulk-action/` - Bulk notification actions
- **POST** `/notifications/mark-all-read/` - Mark all as read
- **GET** `/notifications/stats/` - Get notification statistics

### Notification Preferences
- **GET** `/notifications/preferences/` - Get notification preferences
- **PATCH** `/notifications/preferences/` - Update notification preferences

## WebSocket Connections

### Connection Info
- **GET** `/messages/websocket-info/` - Get WebSocket connection info
- **GET** `/messages/channel/<channel_id>/websocket/` - Get channel WebSocket URL

### WebSocket URLs
- **Channel**: `ws://localhost:8000/ws/channel/<channel_id>/?token=<jwt_token>`
- **User**: `ws://localhost:8000/ws/user/<user_id>/?token=<jwt_token>`

### WebSocket Message Types

#### Sending Messages
```json
{
  "type": "typing_start"
}
```

```json
{
  "type": "typing_stop"
}
```

```json
{
  "type": "message_read",
  "message_id": "message-uuid"
}
```

#### Receiving Messages
```json
{
  "type": "new_message",
  "message": {...},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

```json
{
  "type": "typing_indicator",
  "action": "start|stop",
  "user_id": "user-uuid",
  "user_name": "John Doe"
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error message",
  "details": {...}
}
```

Common HTTP status codes:
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## Rate Limiting

API endpoints are rate-limited to prevent abuse. Current limits:
- Authentication endpoints: 5 requests per minute
- Message sending: 60 requests per minute
- File uploads: 10 requests per minute
- Other endpoints: 100 requests per minute

## Pagination

List endpoints support pagination with the following parameters:
- `page` - Page number (default: 1)
- `page_size` - Items per page (default: 20, max: 100)

Response format:
```json
{
  "count": 100,
  "next": "http://api.example.com/endpoint/?page=3",
  "previous": "http://api.example.com/endpoint/?page=1",
  "results": [...]
}
```

## Security Features

- All messages are encrypted before storage
- JWT token authentication with refresh tokens
- CORS protection
- File upload validation and virus scanning
- Rate limiting
- Input sanitization
- SQL injection protection
