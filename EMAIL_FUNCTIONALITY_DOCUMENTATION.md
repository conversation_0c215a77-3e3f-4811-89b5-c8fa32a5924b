# Email Functionality Documentation

## Overview

This document describes the email functionality implemented in the MyCorr Messaging platform. The system includes utility functions for sending emails and integration with the verification token system.

## Features Implemented

### 1. Email Utility Functions

#### `send_email(recipient_email, subject, message, html_content=None, from_email=None)`

**Location**: `mycorrmessaging/modules/utils.py`

**Purpose**: General-purpose email sending function using Django's email framework.

**Parameters**:
- `recipient_email` (str): Email address of the recipient
- `subject` (str): Email subject line
- `message` (str): Plain text message content
- `html_content` (str, optional): HTML version of the message
- `from_email` (str, optional): Sender email address (uses default if not provided)

**Returns**: `bool` - True if email was sent successfully, False otherwise

**Features**:
- Supports both plain text and HTML emails
- Uses <PERSON>jango's `EmailMultiAlternatives` for HTML emails
- Uses <PERSON><PERSON><PERSON>'s `send_mail` for plain text emails
- Comprehensive error handling and logging
- Automatic fallback to default sender email

#### `send_verification_token_email(recipient_email, verification_token, user_name=None)`

**Location**: `mycorrmessaging/modules/utils.py`

**Purpose**: Specialized function for sending verification token emails with professional formatting.

**Parameters**:
- `recipient_email` (str): Email address of the recipient
- `verification_token` (str): The verification token to send
- `user_name` (str, optional): Name of the user for personalization

**Returns**: `bool` - True if email was sent successfully, False otherwise

**Features**:
- Professional HTML email template with MyCorr Messaging branding
- Responsive design with inline CSS
- Clear verification code display with prominent styling
- Security warnings about token expiration
- Fallback plain text version
- Personalized greeting when user name is provided

### 2. Django Email Configuration

**Location**: `mycorrmessaging/settings/base.py`

**Settings Added**:
```python
# Email Configuration (for notifications)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'  # For development
EMAIL_HOST = env('EMAIL_HOST', default='localhost')
EMAIL_PORT = env('EMAIL_PORT', default=587)
EMAIL_USE_TLS = env('EMAIL_USE_TLS', default=True)
EMAIL_HOST_USER = env('EMAIL_HOST_USER', default='')
EMAIL_HOST_PASSWORD = env('EMAIL_HOST_PASSWORD', default='')
DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL', default='<EMAIL>')
```

**Environment Variables** (to be set in `.env` file):
- `EMAIL_HOST`: SMTP server hostname (e.g., 'smtp.gmail.com')
- `EMAIL_PORT`: SMTP server port (usually 587 for TLS)
- `EMAIL_USE_TLS`: Whether to use TLS encryption (True/False)
- `EMAIL_HOST_USER`: SMTP username/email
- `EMAIL_HOST_PASSWORD`: SMTP password or app-specific password
- `DEFAULT_FROM_EMAIL`: Default sender email address

### 3. Integration with RequestOneTimeTokenSerializerIn

**Location**: `accounts/serializers.py`

**Changes Made**:
1. **Import Added**:
   ```python

from mycorrmessaging.modules.email_messages import send_verification_token_email
   ```

2. **Enhanced `create` Method**:
   - Generates verification token as before
   - Saves encrypted token and expiry to user model
   - **NEW**: Sends verification token via email using the utility function
   - Includes user's full name for personalization
   - Provides error handling for email sending failures
   - Logs email sending status in debug mode

**Updated Flow**:
1. User requests verification code via API
2. System generates 6-digit random token
3. Token is encrypted and stored in database with 15-minute expiry
4. **NEW**: Professional email is sent to user with verification code
5. Response includes success message and token (in debug mode only)

## Email Template Design

### HTML Email Features:
- **Professional Header**: MyCorr Messaging branding with blue theme
- **Clear Content Area**: Well-structured content with proper spacing
- **Prominent Code Display**: Large, bold verification code with blue background
- **Security Warning**: Highlighted warning about 15-minute expiration
- **Responsive Design**: Works well on desktop and mobile devices
- **Consistent Styling**: Uses Bootstrap-inspired color scheme

### Plain Text Fallback:
- Clean, readable format for email clients that don't support HTML
- All essential information included
- Professional tone maintained

## Usage Examples

### Basic Email Sending:
```python
from mycorrmessaging.modules.utils import send_email

success = send_email(
    recipient_email="<EMAIL>",
    subject="Welcome to MyCorr Messaging",
    message="Thank you for joining our platform!"
)
```

### HTML Email Sending:

```python

from accounts.tasks import send_email

html_content = "<h1>Welcome!</h1><p>Thank you for joining our platform!</p>"
success = send_email(
    recipient_email="<EMAIL>",
    subject="Welcome to MyCorr Messaging",
    message="Thank you for joining our platform!",
    html_content=html_content
)
```

### Verification Token Email:

```python

from mycorrmessaging.modules.email_messages import send_verification_token_email

success = send_verification_token_email(
    recipient_email="<EMAIL>",
    verification_token="123456",
    user_name="John Doe"
)
```

## Testing

The email functionality has been thoroughly tested with:
- ✅ Basic plain text email sending
- ✅ HTML email with multipart content
- ✅ Verification token email with professional template
- ✅ Error handling and logging
- ✅ Integration with Django settings
- ✅ Console backend for development testing

## Production Deployment

### Email Backend Configuration:
For production, update the email backend in settings:
```python
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
```

### Recommended Email Services:
- **Gmail**: Use app-specific passwords
- **SendGrid**: Professional email service with good deliverability
- **Amazon SES**: Cost-effective for high volume
- **Mailgun**: Developer-friendly with good API

### Security Considerations:
- Use environment variables for sensitive email credentials
- Enable TLS encryption for SMTP connections
- Use app-specific passwords instead of regular passwords
- Monitor email sending logs for security issues
- Implement rate limiting for email sending

## Error Handling

The email system includes comprehensive error handling:
- **Connection Errors**: Logged with details about SMTP connection issues
- **Authentication Errors**: Logged when email credentials are invalid
- **Sending Failures**: Gracefully handled without breaking the application flow
- **Logging**: All email activities are logged for debugging and monitoring

## Future Enhancements

Potential improvements for the email system:
1. **Email Templates**: Django template-based email generation
2. **Email Queue**: Asynchronous email sending with Celery
3. **Email Analytics**: Track email open rates and click-through rates
4. **Internationalization**: Multi-language email templates
5. **Email Preferences**: User-configurable email notification settings
