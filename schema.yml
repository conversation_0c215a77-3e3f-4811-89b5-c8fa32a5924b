openapi: 3.0.3
info:
  title: MyCorr Messaging API
  version: 1.0.0
  description: API for MyCorrMessaging... comprehensive communication platform
paths:
  /api/auth/activity/{user_id}/:
    get:
      operationId: auth_activity_retrieve
      description: Get user activity information.
      parameters:
      - in: path
        name: user_id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserActivitySerializerOut'
          description: ''
        '404':
          description: No response body
  /api/auth/list/:
    get:
      operationId: auth_list_list
      description: API view for listing users.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserListSerializerOutList'
          description: ''
  /api/auth/login/:
    post:
      operationId: auth_login_create
      description: API view for user login.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserLoginSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserLoginSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserLoginSerializerIn'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileSerializerOut'
          description: ''
  /api/auth/logout/:
    post:
      operationId: auth_logout_create
      description: Logout user by blacklisting refresh token.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserLogoutSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserLogoutSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserLogoutSerializerIn'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserLogoutSerializerOut'
          description: ''
  /api/auth/online-status/:
    post:
      operationId: auth_online_status_create
      description: Set user online/offline status.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserOnlineStatusSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserOnlineStatusSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserOnlineStatusSerializerIn'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserOnlineStatusSerializerOut'
          description: ''
  /api/auth/online-users/:
    get:
      operationId: auth_online_users_retrieve
      description: Get list of online users in user's organizations.
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnlineUsersSerializerOut'
          description: ''
  /api/auth/password/change/:
    post:
      operationId: auth_password_change_create
      description: Change user password.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordChangeSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordChangeSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordChangeSerializerIn'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordChangeSerializerOut'
          description: ''
  /api/auth/preferences/:
    get:
      operationId: auth_preferences_retrieve
      description: Get current user's preferences.
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPreferencesSerializerOut'
          description: ''
    patch:
      operationId: auth_preferences_partial_update
      description: Update current user's preferences.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserPreferencesSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserPreferencesSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserPreferencesSerializerIn'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPreferencesSerializerOut'
          description: ''
  /api/auth/profile/:
    get:
      operationId: auth_profile_retrieve
      description: Get current user's profile.
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileSerializerOut'
          description: ''
    patch:
      operationId: auth_profile_partial_update
      description: Update current user's profile.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserProfileUpdateSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserProfileUpdateSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserProfileUpdateSerializerIn'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileSerializerOut'
          description: ''
  /api/auth/profile/{id}/:
    get:
      operationId: auth_profile_retrieve_2
      description: API view for getting user details.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileSerializerOut'
          description: ''
  /api/auth/register/:
    post:
      operationId: auth_register_create
      description: API view for user registration.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegistrationSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserRegistrationSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserRegistrationSerializerIn'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRegistrationSerializerOut'
          description: ''
  /api/auth/request-verification/:
    post:
      operationId: auth_request_verification_create
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RequestVerificationLinkSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/RequestVerificationLinkSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/RequestVerificationLinkSerializerIn'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
  /api/auth/search/:
    get:
      operationId: auth_search_list
      description: API view for searching users.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserSearchSerializerOutList'
          description: ''
  /api/auth/status/:
    patch:
      operationId: auth_status_partial_update
      description: Update user status.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserStatusUpdateSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserStatusUpdateSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserStatusUpdateSerializerIn'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileSerializerOut'
          description: ''
  /api/auth/token/refresh/:
    post:
      operationId: auth_token_refresh_create
      description: |-
        Takes a refresh type JSON web token and returns an access type JSON web
        token if the refresh token is valid.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRefresh'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenRefresh'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TokenRefresh'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenRefresh'
          description: ''
  /api/auth/update-activity/:
    post:
      operationId: auth_update_activity_create
      description: Update user's last activity timestamp.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LastActivityUpdateSerializerOut'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/LastActivityUpdateSerializerOut'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/LastActivityUpdateSerializerOut'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LastActivityUpdateSerializerOut'
          description: ''
  /api/channels/create/:
    post:
      operationId: channels_create_create
      description: API view for creating channels.
      tags:
      - channels
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChannelCreateSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChannelCreateSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChannelCreateSerializerIn'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelSerializerOut'
          description: ''
  /api/channels/direct-message/:
    post:
      operationId: channels_direct_message_create
      description: Create or get existing direct message channel.
      tags:
      - channels
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DirectMessageCreateSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DirectMessageCreateSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DirectMessageCreateSerializerIn'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelSerializerOut'
          description: ''
  /api/channels/org/{org_slug}/:
    get:
      operationId: channels_org_list
      description: API view for listing organization channels.
      parameters:
      - in: path
        name: org_slug
        schema:
          type: string
        required: true
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - channels
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedChannelListSerializerOutList'
          description: ''
  /api/channels/org/{org_slug}/{channel_slug}/:
    get:
      operationId: channels_org_retrieve
      description: Get channel details.
      parameters:
      - in: path
        name: channel_slug
        schema:
          type: string
        required: true
      - in: path
        name: org_slug
        schema:
          type: string
        required: true
      tags:
      - channels
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelSerializerOut'
          description: ''
    patch:
      operationId: channels_org_partial_update
      description: Update channel details.
      parameters:
      - in: path
        name: channel_slug
        schema:
          type: string
        required: true
      - in: path
        name: org_slug
        schema:
          type: string
        required: true
      tags:
      - channels
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedChannelUpdateSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedChannelUpdateSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedChannelUpdateSerializerIn'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelSerializerOut'
          description: ''
    delete:
      operationId: channels_org_destroy
      description: Delete/archive channel.
      parameters:
      - in: path
        name: channel_slug
        schema:
          type: string
        required: true
      - in: path
        name: org_slug
        schema:
          type: string
        required: true
      tags:
      - channels
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
  /api/channels/org/{org_slug}/{channel_slug}/invite/:
    post:
      operationId: channels_org_invite_create
      description: Invite users to channel.
      parameters:
      - in: path
        name: channel_slug
        schema:
          type: string
        required: true
      - in: path
        name: org_slug
        schema:
          type: string
        required: true
      tags:
      - channels
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChannelInviteSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChannelInviteSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChannelInviteSerializerIn'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
  /api/channels/org/{org_slug}/{channel_slug}/join/:
    post:
      operationId: channels_org_join_create
      description: Join a public channel.
      parameters:
      - in: path
        name: channel_slug
        schema:
          type: string
        required: true
      - in: path
        name: org_slug
        schema:
          type: string
        required: true
      tags:
      - channels
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JoinChannelSerializerOut'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/JoinChannelSerializerOut'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/JoinChannelSerializerOut'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JoinChannelSerializerOut'
          description: ''
        '400':
          description: No response body
  /api/channels/org/{org_slug}/{channel_slug}/leave/:
    post:
      operationId: channels_org_leave_create
      description: Leave a channel.
      parameters:
      - in: path
        name: channel_slug
        schema:
          type: string
        required: true
      - in: path
        name: org_slug
        schema:
          type: string
        required: true
      tags:
      - channels
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LeaveChannelSerializerOut'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/LeaveChannelSerializerOut'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/LeaveChannelSerializerOut'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LeaveChannelSerializerOut'
          description: ''
        '400':
          description: No response body
  /api/channels/org/{org_slug}/{channel_slug}/members/:
    get:
      operationId: channels_org_members_list
      description: API view for listing channel members.
      parameters:
      - in: path
        name: channel_slug
        schema:
          type: string
        required: true
      - in: path
        name: org_slug
        schema:
          type: string
        required: true
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - channels
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedChannelMembershipSerializerOutList'
          description: ''
  /api/channels/org/{org_slug}/{channel_slug}/members/{user_id}/:
    patch:
      operationId: channels_org_members_partial_update
      description: Update member role or settings.
      parameters:
      - in: path
        name: channel_slug
        schema:
          type: string
        required: true
      - in: path
        name: org_slug
        schema:
          type: string
        required: true
      - in: path
        name: user_id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - channels
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedChannelMembershipUpdateSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedChannelMembershipUpdateSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedChannelMembershipUpdateSerializerIn'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelMembershipSerializerOut'
          description: ''
    delete:
      operationId: channels_org_members_destroy
      description: Remove member from channel.
      parameters:
      - in: path
        name: channel_slug
        schema:
          type: string
        required: true
      - in: path
        name: org_slug
        schema:
          type: string
        required: true
      - in: path
        name: user_id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - channels
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
  /api/organizations/{slug}/:
    get:
      operationId: organizations_retrieve
      description: Get organization details.
      parameters:
      - in: path
        name: slug
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationSerializerOut'
          description: ''
    patch:
      operationId: organizations_partial_update
      description: Update organization details.
      parameters:
      - in: path
        name: slug
        schema:
          type: string
        required: true
      tags:
      - organizations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedOrganizationUpdateSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedOrganizationUpdateSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedOrganizationUpdateSerializerIn'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationSerializerOut'
          description: ''
    delete:
      operationId: organizations_destroy
      description: Delete organization.
      parameters:
      - in: path
        name: slug
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteResponseSerializerOut'
          description: ''
  /api/organizations/{slug}/invitations/:
    get:
      operationId: organizations_invitations_retrieve
      description: List organization invitations.
      parameters:
      - in: path
        name: slug
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationInvitationSerializerOut'
          description: ''
    post:
      operationId: organizations_invitations_create
      description: Send organization invitations to multiple email addresses.
      parameters:
      - in: path
        name: slug
        schema:
          type: string
        required: true
      tags:
      - organizations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrganizationInvitationCreateSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/OrganizationInvitationCreateSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/OrganizationInvitationCreateSerializerIn'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationBulkInvitationSerializerOut'
          description: ''
  /api/organizations/{slug}/invitations/{invitation_id}/:
    delete:
      operationId: organizations_invitations_destroy
      description: Cancel organization invitation.
      parameters:
      - in: path
        name: invitation_id
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: slug
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteResponseSerializerOut'
          description: ''
  /api/organizations/{slug}/leave/:
    post:
      operationId: organizations_leave_create
      description: Leave an organization.
      parameters:
      - in: path
        name: slug
        schema:
          type: string
        required: true
      tags:
      - organizations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LeaveOrganizationSerializerOut'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/LeaveOrganizationSerializerOut'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/LeaveOrganizationSerializerOut'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LeaveOrganizationSerializerOut'
          description: ''
        '400':
          description: No response body
  /api/organizations/{slug}/members/:
    get:
      operationId: organizations_members_list
      description: API view for listing organization members.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: path
        name: slug
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedOrganizationMembershipSerializerOutList'
          description: ''
  /api/organizations/{slug}/members/{user_id}/:
    patch:
      operationId: organizations_members_partial_update
      description: Update member role or status.
      parameters:
      - in: path
        name: slug
        schema:
          type: string
        required: true
      - in: path
        name: user_id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - organizations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedOrganizationMembershipUpdateSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedOrganizationMembershipUpdateSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedOrganizationMembershipUpdateSerializerIn'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationMembershipSerializerOut'
          description: ''
    delete:
      operationId: organizations_members_destroy
      description: Remove member from organization.
      parameters:
      - in: path
        name: slug
        schema:
          type: string
        required: true
      - in: path
        name: user_id
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - organizations
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteResponseSerializerOut'
          description: ''
  /api/organizations/{slug}/stats/:
    get:
      operationId: organizations_stats_retrieve
      description: Get organization statistics.
      parameters:
      - in: path
        name: slug
        schema:
          type: string
        required: true
      tags:
      - organizations
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationStatsSerializerOut'
          description: ''
  /api/organizations/create/:
    post:
      operationId: organizations_create_create
      description: API view for creating organizations.
      tags:
      - organizations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrganizationCreateSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/OrganizationCreateSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/OrganizationCreateSerializerIn'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationSerializerOut'
          description: ''
  /api/organizations/invitations/accept/:
    post:
      operationId: organizations_invitations_accept_create
      description: Accept organization invitation.
      tags:
      - organizations
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrganizationInvitationAcceptSerializerIn'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/OrganizationInvitationAcceptSerializerIn'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/OrganizationInvitationAcceptSerializerIn'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrganizationSerializerOut'
          description: ''
  /api/organizations/list/:
    get:
      operationId: organizations_list_list
      description: API view for listing organizations.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - organizations
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedOrganizationListSerializerOutList'
          description: ''
components:
  schemas:
    ChannelCreateSerializerIn:
      type: object
      description: Serializer for creating channels.
      properties:
        name:
          type: string
          description: Channel name
          maxLength: 255
        description:
          type: string
          nullable: true
          description: Channel description
        channel_type:
          allOf:
          - $ref: '#/components/schemas/ChannelTypeEnum'
          description: |-
            Channel type (public, private, direct)

            * `public` - Public
            * `private` - Private
            * `direct` - Direct Message
        is_default:
          type: boolean
          description: Whether this is a default channel for the organization
        allow_threads:
          type: boolean
          description: Allow threaded replies in this channel
        allow_file_uploads:
          type: boolean
          description: Allow file uploads in this channel
        max_members:
          type: integer
          maximum: 2147483647
          minimum: 0
          nullable: true
          description: Maximum number of members (null for unlimited)
        organization_slug:
          type: string
          writeOnly: true
          pattern: ^[-a-zA-Z0-9_]+$
      required:
      - name
      - organization_slug
    ChannelInviteSerializerIn:
      type: object
      description: Serializer for inviting users to channels.
      properties:
        user_ids:
          type: array
          items:
            type: string
            format: uuid
          description: List of user IDs to invite
        role:
          allOf:
          - $ref: '#/components/schemas/ChannelInviteSerializerInRoleEnum'
          default: member
      required:
      - user_ids
    ChannelInviteSerializerInRoleEnum:
      enum:
      - member
      - admin
      type: string
      description: |-
        * `member` - Member
        * `admin` - Admin
    ChannelListSerializerOut:
      type: object
      description: Serializer for channel list output.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          description: Channel name
          maxLength: 255
        slug:
          type: string
          description: URL-friendly channel identifier
          maxLength: 255
          pattern: ^[-a-zA-Z0-9_]+$
        description:
          type: string
          nullable: true
          description: Channel description
        channel_type:
          allOf:
          - $ref: '#/components/schemas/ChannelTypeEnum'
          description: |-
            Channel type (public, private, direct)

            * `public` - Public
            * `private` - Private
            * `direct` - Direct Message
        is_archived:
          type: boolean
          description: Whether channel is archived
        is_default:
          type: boolean
          description: Whether this is a default channel for the organization
        member_count:
          type: string
          readOnly: true
        user_role:
          type: string
          readOnly: true
        is_member:
          type: boolean
          readOnly: true
        unread_count:
          type: integer
          readOnly: true
        last_message_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - id
      - is_member
      - last_message_at
      - member_count
      - name
      - slug
      - unread_count
      - user_role
    ChannelMembershipSerializerOut:
      type: object
      description: Serializer for channel membership output.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        user:
          type: string
          format: uuid
          description: User
        user_name:
          type: string
          readOnly: true
        user_email:
          type: string
          readOnly: true
        user_avatar:
          type: string
          format: uri
          readOnly: true
        user_status:
          type: string
          readOnly: true
        role:
          allOf:
          - $ref: '#/components/schemas/Role5a7Enum'
          description: |-
            User's role in the channel

            * `owner` - Owner
            * `admin` - Admin
            * `member` - Member
        is_active:
          type: boolean
          description: Whether membership is active
        joined_at:
          type: string
          format: date-time
          readOnly: true
          description: When user joined the channel
        invited_by:
          type: string
          format: uuid
          nullable: true
          description: User who invited this member
        invited_by_name:
          type: string
          readOnly: true
        mute_notifications:
          type: boolean
          description: Whether notifications are muted for this channel
        last_read_at:
          type: string
          format: date-time
          nullable: true
          description: Last time user read messages in this channel
      required:
      - id
      - invited_by_name
      - joined_at
      - user
      - user_avatar
      - user_email
      - user_name
      - user_status
    ChannelSerializerOut:
      type: object
      description: Serializer for channel output.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          description: Channel name
          maxLength: 255
        slug:
          type: string
          description: URL-friendly channel identifier
          maxLength: 255
          pattern: ^[-a-zA-Z0-9_]+$
        description:
          type: string
          nullable: true
          description: Channel description
        organization:
          type: string
          format: uuid
          description: Organization this channel belongs to
        organization_name:
          type: string
          readOnly: true
        channel_type:
          allOf:
          - $ref: '#/components/schemas/ChannelTypeEnum'
          description: |-
            Channel type (public, private, direct)

            * `public` - Public
            * `private` - Private
            * `direct` - Direct Message
        is_active:
          type: boolean
          description: Whether channel is active
        is_archived:
          type: boolean
          description: Whether channel is archived
        is_default:
          type: boolean
          description: Whether this is a default channel for the organization
        allow_threads:
          type: boolean
          description: Allow threaded replies in this channel
        allow_file_uploads:
          type: boolean
          description: Allow file uploads in this channel
        max_members:
          type: integer
          maximum: 2147483647
          minimum: 0
          nullable: true
          description: Maximum number of members (null for unlimited)
        member_count:
          type: string
          readOnly: true
        is_at_capacity:
          type: string
          readOnly: true
        created_by:
          type: string
          format: uuid
          description: User who created this channel
        created_by_name:
          type: string
          readOnly: true
        user_role:
          type: string
          readOnly: true
        is_member:
          type: boolean
          readOnly: true
        unread_count:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - created_by
      - created_by_name
      - id
      - is_at_capacity
      - is_member
      - member_count
      - name
      - organization
      - organization_name
      - slug
      - unread_count
      - updated_at
      - user_role
    ChannelTypeEnum:
      enum:
      - public
      - private
      - direct
      type: string
      description: |-
        * `public` - Public
        * `private` - Private
        * `direct` - Direct Message
    DeleteResponseSerializerOut:
      type: object
      description: Generic serializer for delete responses.
      properties:
        message:
          type: string
          description: Success message
      required:
      - message
    DirectMessageCreateSerializerIn:
      type: object
      description: Serializer for creating direct message channels.
      properties:
        user_id:
          type: string
          format: uuid
          description: ID of the user to start DM with
        organization_slug:
          type: string
          description: Organization slug
          pattern: ^[-a-zA-Z0-9_]+$
      required:
      - organization_slug
      - user_id
    JoinChannelSerializerOut:
      type: object
      description: Serializer for join channel response.
      properties:
        message:
          type: string
          description: Success message
      required:
      - message
    LastActivityUpdateSerializerOut:
      type: object
      description: Serializer for last activity update response.
      properties:
        message:
          type: string
          description: Success message
        last_seen:
          type: string
          format: date-time
          description: Updated last seen timestamp
      required:
      - last_seen
      - message
    LeaveChannelSerializerOut:
      type: object
      description: Serializer for leave channel response.
      properties:
        message:
          type: string
          description: Success message
      required:
      - message
    LeaveOrganizationSerializerOut:
      type: object
      description: Serializer for leave organization response.
      properties:
        message:
          type: string
          description: Success message
      required:
      - message
    OnlineUsersSerializerOut:
      type: object
      description: Serializer for online users response.
      properties:
        online_users:
          type: array
          items:
            $ref: '#/components/schemas/UserSearchSerializerOut'
          description: List of online users
        count:
          type: integer
          description: Number of online users
      required:
      - count
      - online_users
    OrganizationBulkInvitationSerializerOut:
      type: object
      description: Serializer for bulk organization invitation response.
      properties:
        message:
          type: string
          description: Success message
        invitations_sent:
          type: integer
          description: Number of invitations sent
        invitations:
          type: array
          items:
            $ref: '#/components/schemas/OrganizationInvitationSerializerOut'
          description: List of created invitations
      required:
      - invitations
      - invitations_sent
      - message
    OrganizationCreateSerializerIn:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        logo:
          type: string
          format: uri
        website:
          type: string
          format: uri
        user_email:
          type: string
          format: email
        first_name:
          type: string
        last_name:
          type: string
      required:
      - first_name
      - last_name
      - name
      - user_email
    OrganizationInvitationAcceptSerializerIn:
      type: object
      description: Serializer for accepting organization invitations.
      properties:
        token:
          type: string
      required:
      - token
    OrganizationInvitationCreateSerializerIn:
      type: object
      description: Serializer for creating organization invitations to multiple email
        addresses.
      properties:
        emails:
          type: array
          items:
            type: string
            format: email
          description: List of email addresses to invite to the organization
          minItems: 1
        role:
          allOf:
          - $ref: '#/components/schemas/OrganizationInvitationCreateSerializerInRoleEnum'
          default: member
          description: |-
            Role to assign to invited users

            * `member` - member
            * `admin` - admin
        message:
          type: string
          description: Optional message to include with the invitation
          maxLength: 500
      required:
      - emails
    OrganizationInvitationCreateSerializerInRoleEnum:
      enum:
      - member
      - admin
      type: string
      description: |-
        * `member` - member
        * `admin` - admin
    OrganizationInvitationSerializerOut:
      type: object
      description: Serializer for organization invitation output.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        organization:
          type: string
          format: uuid
          description: Organization
        organization_name:
          type: string
          readOnly: true
        email:
          type: string
          format: email
          description: Email address of the invitee
          maxLength: 254
        role:
          allOf:
          - $ref: '#/components/schemas/Role691Enum'
          description: |-
            Role to assign to the invitee

            * `owner` - Owner
            * `admin` - Admin
            * `member` - Member
            * `guest` - Guest
        status:
          allOf:
          - $ref: '#/components/schemas/OrganizationInvitationSerializerOutStatusEnum'
          description: |-
            Invitation status

            * `pending` - Pending
            * `accepted` - Accepted
            * `declined` - Declined
            * `expired` - Expired
        token:
          type: string
          description: Unique invitation token
          maxLength: 255
        expires_at:
          type: string
          format: date-time
          description: When the invitation expires
        accepted_at:
          type: string
          format: date-time
          nullable: true
          description: When the invitation was accepted
        message:
          type: string
          nullable: true
          description: Optional message from the inviter
        invited_by:
          type: string
          format: uuid
          description: User who sent the invitation
        invited_by_name:
          type: string
          readOnly: true
        is_expired:
          type: string
          readOnly: true
        is_valid:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - email
      - expires_at
      - id
      - invited_by
      - invited_by_name
      - is_expired
      - is_valid
      - organization
      - organization_name
      - token
    OrganizationInvitationSerializerOutStatusEnum:
      enum:
      - pending
      - accepted
      - declined
      - expired
      type: string
      description: |-
        * `pending` - Pending
        * `accepted` - Accepted
        * `declined` - Declined
        * `expired` - Expired
    OrganizationListSerializerOut:
      type: object
      description: Serializer for organization list output.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          description: Organization name
          maxLength: 255
        slug:
          type: string
          description: URL-friendly organization identifier
          maxLength: 255
          pattern: ^[-a-zA-Z0-9_]+$
        description:
          type: string
          nullable: true
          description: Organization description
        logo:
          type: string
          format: uri
          nullable: true
          description: Organization logo
        member_count:
          type: string
          readOnly: true
        user_role:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - id
      - member_count
      - name
      - slug
      - user_role
    OrganizationMembershipSerializerOut:
      type: object
      description: Serializer for organization membership output.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        user:
          type: string
          format: uuid
          description: User
        user_name:
          type: string
          readOnly: true
        user_email:
          type: string
          readOnly: true
        user_avatar:
          type: string
          format: uri
          readOnly: true
        role:
          allOf:
          - $ref: '#/components/schemas/Role691Enum'
          description: |-
            User's role in the organization

            * `owner` - Owner
            * `admin` - Admin
            * `member` - Member
            * `guest` - Guest
        status:
          allOf:
          - $ref: '#/components/schemas/Status2f2Enum'
          description: |-
            Membership status

            * `active` - Active
            * `inactive` - Inactive
            * `pending` - Pending
            * `suspended` - Suspended
        joined_at:
          type: string
          format: date-time
          readOnly: true
          description: When user joined the organization
        invited_by:
          type: string
          format: uuid
          nullable: true
          description: User who invited this member
        invited_by_name:
          type: string
          readOnly: true
      required:
      - id
      - invited_by_name
      - joined_at
      - user
      - user_avatar
      - user_email
      - user_name
    OrganizationSerializerOut:
      type: object
      description: Serializer for organization output.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          description: Organization name
          maxLength: 255
        slug:
          type: string
          description: URL-friendly organization identifier
          maxLength: 255
          pattern: ^[-a-zA-Z0-9_]+$
        description:
          type: string
          nullable: true
          description: Organization description
        logo:
          type: string
          format: uri
          nullable: true
          description: Organization logo
        website:
          type: string
          format: uri
          nullable: true
          description: Organization website
          maxLength: 200
        is_active:
          type: boolean
          description: Whether organization is active
        max_members:
          type: integer
          maximum: 2147483647
          minimum: 0
          description: Maximum number of members allowed
        member_count:
          type: string
          readOnly: true
        is_at_capacity:
          type: string
          readOnly: true
        allow_public_channels:
          type: boolean
          description: Allow creation of public channels
        require_invitation:
          type: boolean
          description: Require invitation to join organization
        allow_guest_access:
          type: boolean
          description: Allow guest users to join
        created_by:
          type: string
          format: uuid
          description: User who created this organization
        created_by_name:
          type: string
          readOnly: true
        user_role:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - created_by
      - created_by_name
      - id
      - is_at_capacity
      - member_count
      - name
      - slug
      - updated_at
      - user_role
    OrganizationStatsSerializerOut:
      type: object
      description: Serializer for organization statistics.
      properties:
        total_members:
          type: integer
        active_members:
          type: integer
        pending_invitations:
          type: integer
        total_channels:
          type: integer
        public_channels:
          type: integer
        private_channels:
          type: integer
      required:
      - active_members
      - pending_invitations
      - private_channels
      - public_channels
      - total_channels
      - total_members
    PaginatedChannelListSerializerOutList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ChannelListSerializerOut'
    PaginatedChannelMembershipSerializerOutList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ChannelMembershipSerializerOut'
    PaginatedOrganizationListSerializerOutList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/OrganizationListSerializerOut'
    PaginatedOrganizationMembershipSerializerOutList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/OrganizationMembershipSerializerOut'
    PaginatedUserListSerializerOutList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/UserListSerializerOut'
    PaginatedUserSearchSerializerOutList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/UserSearchSerializerOut'
    PasswordChangeSerializerIn:
      type: object
      description: Serializer for password change.
      properties:
        old_password:
          type: string
          writeOnly: true
        new_password:
          type: string
          writeOnly: true
        new_password_confirm:
          type: string
          writeOnly: true
      required:
      - new_password
      - new_password_confirm
      - old_password
    PasswordChangeSerializerOut:
      type: object
      description: Serializer for password change response.
      properties:
        message:
          type: string
          description: Success message
      required:
      - message
    PatchedChannelMembershipUpdateSerializerIn:
      type: object
      description: Serializer for updating channel membership.
      properties:
        role:
          allOf:
          - $ref: '#/components/schemas/Role5a7Enum'
          description: |-
            User's role in the channel

            * `owner` - Owner
            * `admin` - Admin
            * `member` - Member
        mute_notifications:
          type: boolean
          description: Whether notifications are muted for this channel
    PatchedChannelUpdateSerializerIn:
      type: object
      description: Serializer for updating channels.
      properties:
        name:
          type: string
          description: Channel name
          maxLength: 255
        description:
          type: string
          nullable: true
          description: Channel description
        allow_threads:
          type: boolean
          description: Allow threaded replies in this channel
        allow_file_uploads:
          type: boolean
          description: Allow file uploads in this channel
        max_members:
          type: integer
          maximum: 2147483647
          minimum: 0
          nullable: true
          description: Maximum number of members (null for unlimited)
        is_archived:
          type: boolean
          description: Whether channel is archived
    PatchedOrganizationMembershipUpdateSerializerIn:
      type: object
      description: Serializer for updating organization membership.
      properties:
        role:
          allOf:
          - $ref: '#/components/schemas/Role691Enum'
          description: |-
            User's role in the organization

            * `owner` - Owner
            * `admin` - Admin
            * `member` - Member
            * `guest` - Guest
        status:
          allOf:
          - $ref: '#/components/schemas/Status2f2Enum'
          description: |-
            Membership status

            * `active` - Active
            * `inactive` - Inactive
            * `pending` - Pending
            * `suspended` - Suspended
    PatchedOrganizationUpdateSerializerIn:
      type: object
      description: Serializer for updating organizations.
      properties:
        name:
          type: string
          description: Organization name
          maxLength: 255
        description:
          type: string
          nullable: true
          description: Organization description
        logo:
          type: string
          format: uri
          nullable: true
          description: Organization logo
        website:
          type: string
          format: uri
          nullable: true
          description: Organization website
          maxLength: 200
        max_members:
          type: integer
          maximum: 2147483647
          minimum: 0
          description: Maximum number of members allowed
        allow_public_channels:
          type: boolean
          description: Allow creation of public channels
        require_invitation:
          type: boolean
          description: Require invitation to join organization
        allow_guest_access:
          type: boolean
          description: Allow guest users to join
    PatchedUserPreferencesSerializerIn:
      type: object
      description: Serializer for user preferences input.
      properties:
        email_notifications:
          type: boolean
          description: Enable email notifications
        push_notifications:
          type: boolean
          description: Enable push notifications
        desktop_notifications:
          type: boolean
          description: Enable desktop notifications
        notification_sound:
          type: boolean
          description: Enable notification sounds
        show_online_status:
          type: boolean
          description: Show online status to others
        allow_direct_messages:
          type: boolean
          description: Allow direct messages from anyone
        theme:
          allOf:
          - $ref: '#/components/schemas/ThemeEnum'
          description: |-
            UI theme preference

            * `light` - Light
            * `dark` - Dark
            * `auto` - Auto
        language:
          type: string
          description: Preferred language
          maxLength: 10
    PatchedUserProfileUpdateSerializerIn:
      type: object
      description: Serializer for updating user profile.
      properties:
        first_name:
          type: string
          description: User's first name
          maxLength: 150
        last_name:
          type: string
          description: User's last name
          maxLength: 150
        avatar:
          type: string
          format: uri
          nullable: true
          description: User's profile picture
        phone_number:
          type: string
          nullable: true
          description: User's phone number
          maxLength: 20
        bio:
          type: string
          nullable: true
          description: User's bio/description
          maxLength: 500
        status_message:
          type: string
          nullable: true
          description: Custom status message
          maxLength: 200
        timezone:
          type: string
          description: User's timezone
          maxLength: 50
    PatchedUserStatusUpdateSerializerIn:
      type: object
      description: Serializer for updating user status.
      properties:
        status:
          allOf:
          - $ref: '#/components/schemas/Status621Enum'
          description: |-
            User's current status

            * `online` - Online
            * `away` - Away
            * `busy` - Busy
            * `offline` - Offline
            * `do_not_disturb` - Do Not Disturb
        status_message:
          type: string
          nullable: true
          description: Custom status message
          maxLength: 200
    RequestVerificationLinkSerializerIn:
      type: object
      properties:
        email:
          type: string
          format: email
      required:
      - email
    Role5a7Enum:
      enum:
      - owner
      - admin
      - member
      type: string
      description: |-
        * `owner` - Owner
        * `admin` - Admin
        * `member` - Member
    Role691Enum:
      enum:
      - owner
      - admin
      - member
      - guest
      type: string
      description: |-
        * `owner` - Owner
        * `admin` - Admin
        * `member` - Member
        * `guest` - Guest
    Status2f2Enum:
      enum:
      - active
      - inactive
      - pending
      - suspended
      type: string
      description: |-
        * `active` - Active
        * `inactive` - Inactive
        * `pending` - Pending
        * `suspended` - Suspended
    Status621Enum:
      enum:
      - online
      - away
      - busy
      - offline
      - do_not_disturb
      type: string
      description: |-
        * `online` - Online
        * `away` - Away
        * `busy` - Busy
        * `offline` - Offline
        * `do_not_disturb` - Do Not Disturb
    ThemeEnum:
      enum:
      - light
      - dark
      - auto
      type: string
      description: |-
        * `light` - Light
        * `dark` - Dark
        * `auto` - Auto
    TokenRefresh:
      type: object
      properties:
        access:
          type: string
          readOnly: true
        refresh:
          type: string
      required:
      - access
      - refresh
    UserActivitySerializerOut:
      type: object
      description: Serializer for user activity response.
      properties:
        user_id:
          type: string
          format: uuid
          description: User ID
        user_name:
          type: string
          description: User full name
        status:
          type: string
          description: User status
        status_message:
          type: string
          nullable: true
          description: User status message
        is_online:
          type: boolean
          description: Whether user is online
        last_seen:
          type: string
          format: date-time
          nullable: true
          description: Last seen timestamp
        timezone:
          type: string
          description: User timezone
        show_status:
          type: boolean
          description: Whether status is visible
      required:
      - is_online
      - last_seen
      - show_status
      - status
      - status_message
      - timezone
      - user_id
      - user_name
    UserListSerializerOut:
      type: object
      description: Serializer for user list output.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        username:
          type: string
          description: Required. 150 characters or fewer. Letters, digits and @/./+/-/_
            only.
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          description: User's first name
          maxLength: 150
        last_name:
          type: string
          description: User's last name
          maxLength: 150
        full_name:
          type: string
          readOnly: true
        display_name:
          type: string
          readOnly: true
        avatar:
          type: string
          format: uri
          nullable: true
          description: User's profile picture
        status:
          allOf:
          - $ref: '#/components/schemas/Status621Enum'
          description: |-
            User's current status

            * `online` - Online
            * `away` - Away
            * `busy` - Busy
            * `offline` - Offline
            * `do_not_disturb` - Do Not Disturb
        is_online:
          type: boolean
          description: Whether user is currently online
      required:
      - display_name
      - first_name
      - full_name
      - id
      - last_name
      - username
    UserLoginSerializerIn:
      type: object
      description: Serializer for user login input.
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          writeOnly: true
      required:
      - email
      - password
    UserLogoutSerializerIn:
      type: object
      description: Serializer for user logout request.
      properties:
        refresh_token:
          type: string
          description: Refresh token to blacklist
      required:
      - refresh_token
    UserLogoutSerializerOut:
      type: object
      description: Serializer for user logout response.
      properties:
        message:
          type: string
          description: Success message
      required:
      - message
    UserOnlineStatusSerializerIn:
      type: object
      description: Serializer for setting user online status.
      properties:
        is_online:
          type: boolean
          default: true
          description: Set user online/offline status
    UserOnlineStatusSerializerOut:
      type: object
      description: Serializer for user online status response.
      properties:
        message:
          type: string
          description: Status message
        status:
          type: string
          description: User status
        is_online:
          type: boolean
          description: Whether user is online
      required:
      - is_online
      - message
      - status
    UserPreferencesSerializerOut:
      type: object
      description: Serializer for user preferences output.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        email_notifications:
          type: boolean
          description: Enable email notifications
        push_notifications:
          type: boolean
          description: Enable push notifications
        desktop_notifications:
          type: boolean
          description: Enable desktop notifications
        notification_sound:
          type: boolean
          description: Enable notification sounds
        show_online_status:
          type: boolean
          description: Show online status to others
        allow_direct_messages:
          type: boolean
          description: Allow direct messages from anyone
        theme:
          allOf:
          - $ref: '#/components/schemas/ThemeEnum'
          description: |-
            UI theme preference

            * `light` - Light
            * `dark` - Dark
            * `auto` - Auto
        language:
          type: string
          description: Preferred language
          maxLength: 10
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - id
      - updated_at
    UserProfileSerializerOut:
      type: object
      description: Serializer for user profile output.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        email:
          type: string
          format: email
          description: User's email address
          maxLength: 254
        username:
          type: string
          description: Required. 150 characters or fewer. Letters, digits and @/./+/-/_
            only.
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          description: User's first name
          maxLength: 150
        last_name:
          type: string
          description: User's last name
          maxLength: 150
        full_name:
          type: string
          readOnly: true
        display_name:
          type: string
          readOnly: true
        avatar:
          type: string
          format: uri
          nullable: true
          description: User's profile picture
        phone_number:
          type: string
          nullable: true
          description: User's phone number
          maxLength: 20
        bio:
          type: string
          nullable: true
          description: User's bio/description
          maxLength: 500
        status:
          allOf:
          - $ref: '#/components/schemas/Status621Enum'
          description: |-
            User's current status

            * `online` - Online
            * `away` - Away
            * `busy` - Busy
            * `offline` - Offline
            * `do_not_disturb` - Do Not Disturb
        status_message:
          type: string
          nullable: true
          description: Custom status message
          maxLength: 200
        last_seen:
          type: string
          format: date-time
          nullable: true
          description: Last time user was active
        is_online:
          type: boolean
          description: Whether user is currently online
        timezone:
          type: string
          description: User's timezone
          maxLength: 50
        email_verified:
          type: boolean
          description: Whether user's email is verified
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - display_name
      - email
      - first_name
      - full_name
      - id
      - last_name
      - updated_at
      - username
    UserRegistrationSerializerIn:
      type: object
      description: Serializer for user registration input.
      properties:
        email:
          type: string
          format: email
          description: User's email address
          maxLength: 254
        username:
          type: string
          description: Required. 150 characters or fewer. Letters, digits and @/./+/-/_
            only.
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          description: User's first name
          maxLength: 150
        last_name:
          type: string
          description: User's last name
          maxLength: 150
        password:
          type: string
          writeOnly: true
        password_confirm:
          type: string
          writeOnly: true
        phone_number:
          type: string
          nullable: true
          description: User's phone number
          maxLength: 20
      required:
      - email
      - first_name
      - last_name
      - password
      - password_confirm
      - username
    UserRegistrationSerializerOut:
      type: object
      description: Serializer for user registration output.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        email:
          type: string
          format: email
          description: User's email address
          maxLength: 254
        username:
          type: string
          description: Required. 150 characters or fewer. Letters, digits and @/./+/-/_
            only.
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          description: User's first name
          maxLength: 150
        last_name:
          type: string
          description: User's last name
          maxLength: 150
        created_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - email
      - first_name
      - id
      - last_name
      - username
    UserSearchSerializerOut:
      type: object
      description: Serializer for user search results.
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        email:
          type: string
          format: email
          description: User's email address
          maxLength: 254
        username:
          type: string
          description: Required. 150 characters or fewer. Letters, digits and @/./+/-/_
            only.
          pattern: ^[\w.@+-]+$
          maxLength: 150
        first_name:
          type: string
          description: User's first name
          maxLength: 150
        last_name:
          type: string
          description: User's last name
          maxLength: 150
        full_name:
          type: string
          readOnly: true
        display_name:
          type: string
          readOnly: true
        avatar:
          type: string
          format: uri
          nullable: true
          description: User's profile picture
        status:
          allOf:
          - $ref: '#/components/schemas/Status621Enum'
          description: |-
            User's current status

            * `online` - Online
            * `away` - Away
            * `busy` - Busy
            * `offline` - Offline
            * `do_not_disturb` - Do Not Disturb
        is_online:
          type: boolean
          description: Whether user is currently online
        last_seen:
          type: string
          format: date-time
          nullable: true
          description: Last time user was active
      required:
      - display_name
      - email
      - first_name
      - full_name
      - id
      - last_name
      - username
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
    jwtAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
