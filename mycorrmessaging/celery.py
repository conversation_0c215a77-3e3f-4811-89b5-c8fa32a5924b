import os
import logging
from celery import Celery
from decouple import config

# Set up logging
logger = logging.getLogger('celery')

if config('env', '') == 'prod' or os.getenv('env', 'dev') == 'prod':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mycorrmessaging.settings.prod')
else:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mycorrmessaging.settings.dev')

app = Celery("mycorrmessaging")
app.config_from_object("django.conf:settings", namespace="CELERY")
app.autodiscover_tasks()

# Configure Celery logging
app.conf.update(
    worker_hijack_root_logger=False,
    worker_log_format='[%(asctime)s: %(levelname)s/%(processName)s] %(message)s',
    worker_task_log_format='[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s',
)
