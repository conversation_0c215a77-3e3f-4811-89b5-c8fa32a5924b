from accounts.tasks import send_email


def send_verification_token_email(recipient_email: str, verification_token: str, user_name: str = None) -> bool:
    """
    Send verification token email to user.

    Args:
        recipient_email (str): Email address of the recipient
        verification_token (str): The verification token to send
        user_name (str, optional): Name of the user for personalization

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    # Create email subject
    subject = "MyCorr Messaging - Email Verification Code"

    # Create personalized greeting
    greeting = f"Hello {user_name}," if user_name else "Hello,"

    # Create plain text message
    plain_message = f"""
{greeting}

You have requested a verification code for your MyCorr Messaging account.

Your verification code is: {verification_token}

This code will expire in 15 minutes for security reasons.

If you did not request this code, please ignore this email.

Best regards,
The MyCorr Messaging Team
    """.strip()

    # Create HTML message
    html_message = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification Code</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }}
            .header {{
                background-color: #007bff;
                color: white;
                padding: 20px;
                text-align: center;
                border-radius: 8px 8px 0 0;
            }}
            .content {{
                background-color: #f8f9fa;
                padding: 30px;
                border-radius: 0 0 8px 8px;
                border: 1px solid #dee2e6;
            }}
            .verification-code {{
                background-color: #007bff;
                color: white;
                font-size: 24px;
                font-weight: bold;
                padding: 15px;
                text-align: center;
                border-radius: 5px;
                margin: 20px 0;
                letter-spacing: 3px;
            }}
            .warning {{
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
            }}
            .footer {{
                text-align: center;
                margin-top: 30px;
                color: #6c757d;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>MyCorr Messaging</h1>
            <p>Email Verification Code</p>
        </div>
        <div class="content">
            <p>{greeting}</p>

            <p>You have requested a verification code for your MyCorr Messaging account.</p>

            <div class="verification-code">
                {verification_token}
            </div>

            <p>Please enter this code to verify your email address.</p>

            <div class="warning">
                <strong>Important:</strong> This code will expire in 15 minutes for security reasons.
            </div>

            <p>If you did not request this code, please ignore this email.</p>

            <div class="footer">
                <p>Best regards,<br>The MyCorr Messaging Team</p>
            </div>
        </div>
    </body>
    </html>
    """.strip()

    # Send email using celery
    send_email.delay(
        recipient_email=recipient_email,
        subject=subject,
        message=plain_message,
        html_content=html_message
    )

    return True


def send_organization_invitation_email(recipient_email: str, organization_name: str, invitation_url: str) -> bool:
    """
    Send organization invitation email to user.

    Args:
        recipient_email (str): Email address of the recipient
        organization_name (str): Name of the organization
        invitation_token (str): The invitation token to send

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    # Create email subject
    subject = f"Invitation to join {organization_name} on MyCorr Messaging"
    # Create personalized greeting
    greeting = f"Hello,"
    # Create plain text message
    plain_message = f"""
    {greeting}

    You have been invited to join {organization_name} on MyCorr Messaging.

    Please use click the below link to view the invitation.
    
    {invitation_url}

    If you did not request this invitation, please ignore this email.

    Best regards,
    The MyCorr Messaging Team
    """.strip()
    # Create HTML message
    html_message = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Organization Invitation</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }}
            .header {{
                background-color: #007bff;
                color: white;
                padding: 20px;
                text-align: center;
                border-radius: 8px 8px 0 0;
            }}
            .content {{
                background-color: #f8f9fa;
                padding: 30px;
                border-radius: 0 0 8px 8px;
                border: 1px solid #dee2e6;
            }}
            .invitation-token {{
                background-color: #007bff;
                color: white;
                font-size: 24px;
                font-weight: bold;
                padding: 15px;
                text-align: center;
                border-radius: 5px;
                margin: 20px 0;
                letter-spacing: 3px;
            }}
            .warning {{
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
            }}
            .footer {{
                text-align: center;
                margin-top: 30px;
                color: #6c757d;
                font-size: 14px;
            }}
            .button {{
                display: inline-block;
                padding: 10px 20px;
                background-color: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>MyCorr Messaging</h1>
            <p>Organization Invitation</p>
        </div>
        <div class="content">
            <p>{greeting}</p>

            <p>You have been invited to join {organization_name} on MyCorr Messaging.</p>

            <p>Please click the below button to view the invitation.</p>
            
            <div class="invitation-token">
                <button class="button">
                    <a href="{invitation_url}">View Invitation</a>
                </button>
            </div>


            <div class="warning">
                <strong>Important:</strong> This invitation will expire in an hour for security reasons.
            </div>

            <p>If you did not request this invitation, please ignore this email.</p>

            <div class="footer">
                <p>Best regards,<br>The MyCorr Messaging Team</p>
            </div>
        </div>
    </body>
    </html>
    """.strip()

    # Send email using celery
    send_email.delay(
        recipient_email=recipient_email,
        subject=subject,
        message=plain_message,
        html_content=html_message
    )

    return True
