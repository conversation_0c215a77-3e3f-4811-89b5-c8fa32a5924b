from django.contrib.auth.models import AbstractUser, UserManager
from django.db import models
from django.utils import timezone
from home.models import BaseModel
from mycorrmessaging.modules.choices import USER_STATUS_CHOICES
from mycorrmessaging.modules.utils import encrypt_text, decrypt_text


class CustomUserManager(UserManager):
    """Custom user manager for email-based authentication."""

    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(email, password, **extra_fields)


class User(AbstractUser, BaseModel):
    """
    Custom User model extending Django's AbstractUser with additional fields
    for the messaging platform.
    """
    email = models.EmailField(unique=True, help_text="User's email address")
    first_name = models.CharField(max_length=150, help_text="User's first name")
    last_name = models.CharField(max_length=150, help_text="User's last name")
    avatar = models.TextField(
        null=True,
        blank=True,
        help_text="URL to user's profile picture stored in cloud storage"
    )
    phone_number = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        help_text="User's phone number"
    )
    bio = models.TextField(
        max_length=500,
        null=True,
        blank=True,
        help_text="User's bio/description"
    )
    status = models.CharField(
        max_length=20,
        choices=USER_STATUS_CHOICES,
        default='offline',
        help_text="User's current status"
    )
    status_message = models.CharField(
        max_length=200,
        null=True,
        blank=True,
        help_text="Custom status message"
    )
    last_seen = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Last time user was active"
    )
    is_online = models.BooleanField(
        default=False,
        help_text="Whether user is currently online"
    )
    timezone = models.CharField(
        max_length=50,
        default='UTC',
        help_text="User's timezone"
    )
    email_verified = models.BooleanField(
        default=False,
        help_text="Whether user's email is verified"
    )
    password_changed = models.BooleanField(
        default=False,
        help_text="Whether user has changed password after signup"
    )
    email_verification_token = models.TextField(
        null=True,
        blank=True,
        help_text="Token for email verification"
    )
    email_verification_token_expiry = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Expiry time for email verification token"
    )

    # Use email as the username field
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']

    objects = CustomUserManager()

    class Meta:
        db_table = 'accounts_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"

    def get_full_name(self):
        """Return the user's full name."""
        return f"{self.first_name} {self.last_name}".strip()

    def get_short_name(self):
        """Return the user's first name."""
        return self.first_name

    def update_last_seen(self):
        """Update the user's last seen timestamp."""
        self.last_seen = timezone.now()
        self.save(update_fields=['last_seen'])

    def set_online_status(self, is_online=True):
        """Set user's online status."""
        self.is_online = is_online
        if is_online:
            self.status = 'online'
            self.update_last_seen()
        else:
            self.status = 'offline'
        self.save(update_fields=['is_online', 'status'])

    def get_display_name(self):
        """Get the display name for the user."""
        full_name = self.get_full_name()
        return full_name if full_name else self.username


class UserSession(BaseModel):
    """
    Model to track user sessions for better session management.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sessions',
        help_text="User associated with this session"
    )
    session_key = models.CharField(
        max_length=40,
        unique=True,
        help_text="Django session key"
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="IP address of the session"
    )
    user_agent = models.TextField(
        null=True,
        blank=True,
        help_text="User agent string"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether session is active"
    )
    last_activity = models.DateTimeField(
        auto_now=True,
        help_text="Last activity timestamp"
    )

    class Meta:
        db_table = 'accounts_user_session'
        verbose_name = 'User Session'
        verbose_name_plural = 'User Sessions'
        ordering = ['-last_activity']

    def __str__(self):
        return f"{self.user.email} - {self.session_key[:8]}..."


class UserPreferences(BaseModel):
    """
    Model to store user preferences and settings.
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='preferences',
        help_text="User associated with these preferences"
    )

    # Notification preferences
    email_notifications = models.BooleanField(
        default=True,
        help_text="Enable email notifications"
    )
    push_notifications = models.BooleanField(
        default=True,
        help_text="Enable push notifications"
    )
    desktop_notifications = models.BooleanField(
        default=True,
        help_text="Enable desktop notifications"
    )
    notification_sound = models.BooleanField(
        default=True,
        help_text="Enable notification sounds"
    )

    # Privacy preferences
    show_online_status = models.BooleanField(
        default=True,
        help_text="Show online status to others"
    )
    allow_direct_messages = models.BooleanField(
        default=True,
        help_text="Allow direct messages from anyone"
    )

    # Display preferences
    theme = models.CharField(
        max_length=20,
        choices=[('light', 'Light'), ('dark', 'Dark'), ('auto', 'Auto')],
        default='auto',
        help_text="UI theme preference"
    )
    language = models.CharField(
        max_length=10,
        default='en',
        help_text="Preferred language"
    )

    class Meta:
        db_table = 'accounts_user_preferences'
        verbose_name = 'User Preferences'
        verbose_name_plural = 'User Preferences'

    def __str__(self):
        return f"{self.user.email} - Preferences"
