from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

app_name = 'accounts'

urlpatterns = [
    # Authentication endpoints
    # path('register/', views.UserRegistrationAPIView.as_view(), name='register'),
    path('login/', views.UserLoginAPIView.as_view(), name='login'),
    path('logout/', views.UserLogoutAPIView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    
    # Profile management
    path('profile/', views.UserProfileAPIView.as_view(), name='profile'),
    path('profile/<uuid:id>/', views.UserDetailAPIView.as_view(), name='user_detail'),
    path('status/', views.UserStatusAPIView.as_view(), name='status'),
    path('preferences/', views.UserPreferencesAPIView.as_view(), name='preferences'),
    path('password/change/', views.PasswordChangeAPIView.as_view(), name='password_change'),
    
    # User discovery
    path('search/', views.UserSearchAPIView.as_view(), name='search'),
    path('list/', views.UserListAPIView.as_view(), name='list'),
    
    # Status management
    path('online-status/', views.UserOnlineStatusAPIView.as_view(), name='online_status'),
    path('online-users/', views.OnlineUsersAPIView.as_view(), name='online_users'),
    path('activity/<uuid:user_id>/', views.UserActivityAPIView.as_view(), name='user_activity'),
    path('update-activity/', views.UpdateLastActivityAPIView.as_view(), name='update_activity'),

    # Verification
    path('request-verification/', views.RequestVerificationCodeAPIView.as_view(), name='request_verification'),
    path('verify-token/', views.ValidateVerificationCodeAPIView.as_view(), name='verify-token'),
]
