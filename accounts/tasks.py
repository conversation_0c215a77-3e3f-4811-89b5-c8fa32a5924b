import logging

from celery import shared_task
from django.conf import settings
from django.core.mail import EmailMultiAlternatives, send_mail


@shared_task()
def send_email(recipient_email: str, subject: str, message: str, html_content: str = None, from_email: str = None) -> bool:
    """
    Send email using Django's email framework.

    Args:
        recipient_email (str): Email address of the recipient
        subject (str): Email subject line
        message (str): Plain text message content
        html_content (str, optional): HTML version of the message
        from_email (str, optional): Sender email address (uses default if not provided)

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    logger = logging.getLogger(__name__)

    try:
        # Use default from email if not provided
        if not from_email:
            from_email = settings.EMAIL_HOST_USER or settings.DEFAULT_FROM_EMAIL

        if html_content:
            # Send email with both plain text and HTML versions
            email = EmailMultiAlternatives(
                subject=subject,
                body=message,
                from_email=from_email,
                to=[recipient_email]
            )
            email.attach_alternative(html_content, "text/html")
            email.send()
        else:
            # Send plain text email
            send_mail(
                subject=subject,
                message=message,
                from_email=from_email,
                recipient_list=[recipient_email],
                fail_silently=False
            )

        logger.info(f"Email sent successfully to {recipient_email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send email to {recipient_email}: {str(e)}")
        return False
