from rest_framework import status, generics, permissions, serializers
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import login, logout
from django.db.models import Q
from drf_spectacular.utils import extend_schema
from mycorrmessaging.modules.exceptions import raise_serializer_error_msg, InvalidRequestException
from mycorrmessaging.modules.paginations import CustomPagination
from .models import User, UserPreferences
from .serializers import (
    UserRegistrationSerializerIn, UserRegistrationSerializerOut,
    UserLoginSerializerIn, UserProfileSerializerOut, UserProfileUpdateSerializerIn,
    UserStatusUpdateSerializerIn, UserPreferencesSerializerIn, UserPreferencesSerializerOut,
    PasswordChangeSerializerIn, UserSearchSerializerOut, UserListSerializerOut,
    UserOnlineStatusSerializerIn, UserOnlineStatusSerializerOut, OnlineUsersSerializerOut,
    UserActivitySerializerOut, LastActivityUpdateSerializerOut, UserLogoutSerializerIn,
    UserLogoutSerializerOut, PasswordChangeSerializerOut, RequestOneTimeTokenSerializerIn, VerifyOneTimeTokenSerializerIn
)


class UserRegistrationAPIView(APIView):
    """API view for user registration."""
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        request=UserRegistrationSerializerIn,
        responses={status.HTTP_201_CREATED: UserRegistrationSerializerOut}
    )
    def post(self, request):
        serializer = UserRegistrationSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        user = serializer.save()

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        response_data = UserRegistrationSerializerOut(user).data
        response_data.update({
            'access_token': str(access_token),
            'refresh_token': str(refresh),
            'message': 'User registered successfully'
        })

        return Response(response_data, status=status.HTTP_201_CREATED)


class UserLoginAPIView(APIView):
    """API view for user login."""
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        request=UserLoginSerializerIn,
        responses={status.HTTP_200_OK: UserProfileSerializerOut}
    )
    def post(self, request):
        serializer = UserLoginSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        user = serializer.validated_data['user']
        login(request, user)

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        response_data = {
            'access_token': str(access_token),
            'refresh_token': str(refresh),
            'message': 'Login successful',
            'email_verified': user.email_verified,
            'password_changed': user.password_changed
        }

        return Response(response_data, status=status.HTTP_200_OK)


class UserLogoutAPIView(APIView):
    """API view for user logout."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=UserLogoutSerializerIn,
        responses={status.HTTP_200_OK: UserLogoutSerializerOut}
    )
    def post(self, request):
        """Logout user by blacklisting refresh token."""
        serializer = UserLogoutSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        try:
            refresh_token = serializer.validated_data['refresh_token']
            token = RefreshToken(refresh_token)
            token.blacklist()
        except Exception:
            pass

        logout(request)
        return Response({'message': 'Logout successful'}, status=status.HTTP_200_OK)


class UserProfileAPIView(APIView):
    """API view for user profile management."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: UserProfileSerializerOut})
    def get(self, request):
        """Get current user's profile."""
        serializer = UserProfileSerializerOut(request.user, context={"request": request})
        return Response(serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        request=UserProfileUpdateSerializerIn,
        responses={status.HTTP_200_OK: UserProfileSerializerOut}
    )
    def patch(self, request):
        """Update current user's profile."""
        serializer = UserProfileUpdateSerializerIn(
            request.user,
            data=request.data,
            partial=True
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        user = serializer.save()
        response_data = UserProfileSerializerOut(user).data
        response_data['message'] = 'Profile updated successfully'

        return Response(response_data, status=status.HTTP_200_OK)


class UserStatusAPIView(APIView):
    """API view for updating user status."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=UserStatusUpdateSerializerIn,
        responses={status.HTTP_200_OK: UserProfileSerializerOut}
    )
    def patch(self, request):
        """Update user status."""
        serializer = UserStatusUpdateSerializerIn(
            request.user,
            data=request.data,
            partial=True
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        user = serializer.save()
        response_data = UserProfileSerializerOut(user).data
        response_data['message'] = 'Status updated successfully'

        return Response(response_data, status=status.HTTP_200_OK)


class UserPreferencesAPIView(APIView):
    """API view for user preferences management."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: UserPreferencesSerializerOut})
    def get(self, request):
        """Get current user's preferences."""
        preferences, created = UserPreferences.objects.get_or_create(user=request.user)
        serializer = UserPreferencesSerializerOut(preferences)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @extend_schema(
        request=UserPreferencesSerializerIn,
        responses={status.HTTP_200_OK: UserPreferencesSerializerOut}
    )
    def patch(self, request):
        """Update current user's preferences."""
        preferences, created = UserPreferences.objects.get_or_create(user=request.user)
        serializer = UserPreferencesSerializerIn(
            preferences,
            data=request.data,
            partial=True
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        preferences = serializer.save()
        response_data = UserPreferencesSerializerOut(preferences).data
        response_data['message'] = 'Preferences updated successfully'

        return Response(response_data, status=status.HTTP_200_OK)


class PasswordChangeAPIView(APIView):
    """API view for changing user password."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=PasswordChangeSerializerIn,
        responses={status.HTTP_200_OK: PasswordChangeSerializerOut}
    )
    def post(self, request):
        """Change user password."""
        serializer = PasswordChangeSerializerIn(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        if not user.password_changed:
            user.password_changed = True
        if not user.email_verified:
            user.email_verified = True
        user.save()

        return Response(
            {'message': 'Password changed successfully'},
            status=status.HTTP_200_OK
        )


class UserSearchAPIView(generics.ListAPIView):
    """API view for searching users."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserSearchSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        query = self.request.query_params.get('q', '')
        if not query:
            return User.objects.none()

        # Query should return other users in user's organizations
        from organizations.models import Organization
        user_orgs = Organization.objects.filter(
            memberships__user=self.request.user,
            memberships__status='active',
            is_active=True
        )
        return User.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(username__icontains=query) |
            Q(email__icontains=query)
        ).exclude(id=self.request.user.id).filter(
            organization_memberships__organization__in=user_orgs,
            organization_memberships__status='active'
        )

        # return User.objects.filter(
        #     Q(first_name__icontains=query) |
        #     Q(last_name__icontains=query) |
        #     Q(username__icontains=query) |
        #     Q(email__icontains=query)
        # ).exclude(id=self.request.user.id)


class UserListAPIView(generics.ListAPIView):
    """API view for listing users."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserListSerializerOut
    pagination_class = CustomPagination

    def get_queryset(self):
        return User.objects.filter(is_active=True).exclude(id=self.request.user.id)


class UserDetailAPIView(generics.RetrieveAPIView):
    """API view for getting user details."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserProfileSerializerOut
    queryset = User.objects.filter(is_active=True)
    lookup_field = 'id'


class UserOnlineStatusAPIView(APIView):
    """API view for setting user online/offline status."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        request=UserOnlineStatusSerializerIn,
        responses={status.HTTP_200_OK: UserOnlineStatusSerializerOut}
    )
    def post(self, request):
        """Set user online/offline status."""
        serializer = UserOnlineStatusSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)

        is_online = serializer.validated_data.get('is_online', True)
        request.user.set_online_status(is_online)

        return Response({
            'message': f'Status set to {"online" if is_online else "offline"}',
            'status': request.user.status,
            'is_online': request.user.is_online
        }, status=status.HTTP_200_OK)


class OnlineUsersAPIView(APIView):
    """API view for getting list of online users in user's organizations."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={status.HTTP_200_OK: OnlineUsersSerializerOut})
    def get(self, request):
        """Get list of online users in user's organizations."""
        from organizations.models import Organization

        # Get organizations user is member of
        user_orgs = Organization.objects.filter(
            memberships__user=request.user,
            memberships__status='active',
            is_active=True
        )

        # Get online users from these organizations
        online_users = User.objects.filter(
            organization_memberships__organization__in=user_orgs,
            organization_memberships__status='active',
            is_online=True,
            is_active=True
        ).exclude(id=request.user.id).distinct()

        serializer = UserListSerializerOut(online_users, many=True)
        return Response({
            'online_users': serializer.data,
            'count': online_users.count()
        }, status=status.HTTP_200_OK)


class UserActivityAPIView(APIView):
    """API view for getting user activity information."""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(responses={
        status.HTTP_200_OK: UserActivitySerializerOut,
        status.HTTP_404_NOT_FOUND: serializers.Serializer
    })
    def get(self, request, user_id):
        """Get user activity information."""
        try:
            user = User.objects.get(id=user_id, is_active=True)

            # Check if requesting user can see this user's activity
            # (they must be in the same organization)
            from organizations.models import Organization

            common_orgs = Organization.objects.filter(
                memberships__user=request.user,
                memberships__status='active'
            ).filter(
                memberships__user=user,
                memberships__status='active'
            )

            if not common_orgs.exists():
                raise InvalidRequestException({"message": "You don't have permission to view this user's activity."})

            # Get user activity data
            activity_data = {
                'user_id': str(user.id),
                'user_name': user.get_full_name(),
                'status': user.status,
                'status_message': user.status_message,
                'is_online': user.is_online,
                'last_seen': user.last_seen.isoformat() if user.last_seen else None,
                'timezone': user.timezone,
            }

            # Add privacy-respecting information
            if user.preferences.show_online_status:
                activity_data['show_status'] = True
            else:
                activity_data['show_status'] = False
                activity_data['status'] = 'offline'
                activity_data['is_online'] = False

            return Response(activity_data, status=status.HTTP_200_OK)

        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class UpdateLastActivityAPIView(APIView):
    """API view for updating user's last activity timestamp."""
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = LastActivityUpdateSerializerOut

    @extend_schema(responses={status.HTTP_200_OK: LastActivityUpdateSerializerOut})
    def post(self, request):
        """Update user's last activity timestamp."""
        request.user.update_last_seen()

        return Response({
            'message': 'Last activity updated',
            'last_seen': request.user.last_seen.isoformat()
        }, status=status.HTTP_200_OK)


class RequestVerificationCodeAPIView(APIView):
    permission_classes = []

    @extend_schema(request=RequestOneTimeTokenSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = RequestOneTimeTokenSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class ValidateVerificationCodeAPIView(APIView):
    permission_classes = []

    @extend_schema(request=VerifyOneTimeTokenSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = VerifyOneTimeTokenSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)

