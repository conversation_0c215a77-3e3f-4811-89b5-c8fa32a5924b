from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth.signals import user_logged_in, user_logged_out
from .models import User, UserPreferences


@receiver(post_save, sender=User)
def create_user_preferences(sender, instance, created, **kwargs):
    """
    Create UserPreferences instance when a new user is created.
    """
    if created:
        UserPreferences.objects.create(user=instance)


@receiver(user_logged_in)
def user_logged_in_handler(sender, request, user, **kwargs):
    """
    Handle user login - set online status and update last seen.
    """
    user.set_online_status(True)


@receiver(user_logged_out)
def user_logged_out_handler(sender, request, user, **kwargs):
    """
    Handle user logout - set offline status.
    """
    if user:
        user.set_online_status(False)


@receiver(post_save, sender=User)
def handle_user_status_update(sender, instance, created, **kwargs):
    """
    Handle user status updates and send real-time notifications.
    """
    if not created:
        # Check if status or status_message was updated
        if hasattr(instance, '_state') and instance._state.fields_cache:
            try:
                from messaging.utils import send_user_status_update_to_channels
                send_user_status_update_to_channels(str(instance.id), {
                    'user_id': str(instance.id),
                    'user_name': instance.get_full_name(),
                    'status': instance.status,
                    'status_message': instance.status_message,
                    'is_online': instance.is_online,
                    'timestamp': instance.updated_at.isoformat()
                })
            except Exception:
                pass
