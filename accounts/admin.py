from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, UserSession, UserPreferences


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin configuration for User model."""

    list_display = [
        'email', 'username', 'first_name', 'last_name', 'status',
        'is_online', 'email_verified', 'is_active', 'created_at'
    ]
    list_filter = [
        'status', 'is_online', 'email_verified', 'is_active',
        'is_staff', 'is_superuser', 'created_at'
    ]
    search_fields = ['email', 'username', 'first_name', 'last_name']
    ordering = ['-created_at']
    readonly_fields = ['id', 'created_at', 'updated_at', 'last_login', 'date_joined']

    fieldsets = (
        (None, {
            'fields': ('email', 'username', 'password')
        }),
        ('Personal info', {
            'fields': ('first_name', 'last_name', 'avatar', 'phone_number', 'bio')
        }),
        ('Status & Preferences', {
            'fields': ('status', 'status_message', 'is_online', 'last_seen', 'timezone')
        }),
        ('Email Verification', {
            'fields': ('email_verified', 'email_verification_token')
        }),
        ('Permissions', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
        ('Important dates', {
            'fields': ('last_login', 'date_joined', 'created_at', 'updated_at')
        }),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'username', 'first_name', 'last_name', 'password1', 'password2'),
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('preferences')


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """Admin configuration for UserSession model."""

    list_display = [
        'user', 'session_key_short', 'ip_address', 'is_active',
        'last_activity', 'created_at'
    ]
    list_filter = ['is_active', 'created_at', 'last_activity']
    search_fields = ['user__email', 'user__username', 'session_key', 'ip_address']
    readonly_fields = ['id', 'created_at', 'updated_at']
    ordering = ['-last_activity']

    def session_key_short(self, obj):
        return f"{obj.session_key[:8]}..." if obj.session_key else ""
    session_key_short.short_description = "Session Key"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(UserPreferences)
class UserPreferencesAdmin(admin.ModelAdmin):
    """Admin configuration for UserPreferences model."""

    list_display = [
        'user', 'email_notifications', 'push_notifications',
        'theme', 'language', 'created_at'
    ]
    list_filter = [
        'email_notifications', 'push_notifications', 'desktop_notifications',
        'theme', 'language', 'created_at'
    ]
    search_fields = ['user__email', 'user__username']
    readonly_fields = ['id', 'created_at', 'updated_at']

    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Notification Preferences', {
            'fields': (
                'email_notifications', 'push_notifications',
                'desktop_notifications', 'notification_sound'
            )
        }),
        ('Privacy Preferences', {
            'fields': ('show_online_status', 'allow_direct_messages')
        }),
        ('Display Preferences', {
            'fields': ('theme', 'language')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')
