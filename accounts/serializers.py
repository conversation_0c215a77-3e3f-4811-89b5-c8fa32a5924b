import datetime

from django.conf import settings
from django.utils import timezone
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password

from mycorrmessaging.modules.exceptions import InvalidRequestException
from mycorrmessaging.modules.utils import generate_random_otp, encrypt_text, get_next_minute, decrypt_text
from mycorrmessaging.modules.email_messages import send_verification_token_email
from organizations.serializers import OrganizationSerializerOut
from .models import User, UserPreferences


class UserRegistrationSerializerIn(serializers.ModelSerializer):
    """Serializer for user registration input."""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'email', 'username', 'first_name', 'last_name', 
            'password', 'password_confirm', 'phone_number'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise InvalidRequestException({"message": "Passwords don't match."})
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        user = User.objects.create_user(**validated_data)
        return user


class UserRegistrationSerializerOut(serializers.ModelSerializer):
    """Serializer for user registration output."""
    class Meta:
        model = User
        fields = ['id', 'email', 'username', 'first_name', 'last_name', 'created_at']


class UserLoginSerializerIn(serializers.Serializer):
    """Serializer for user login input."""
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(username=email, password=password)
            if not user:
                raise InvalidRequestException({'message': 'Invalid credentials.'})
            if not user.is_active:
                raise InvalidRequestException({'message': 'User account is disabled.'})
            attrs['user'] = user
        else:
            raise InvalidRequestException({'message': 'Must include email and password.'})
        
        return attrs


class UserProfileSerializerOut(serializers.ModelSerializer):
    """Serializer for user profile output."""
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    display_name = serializers.CharField(source='get_display_name', read_only=True)
    organizations = serializers.SerializerMethodField()

    def get_organizations(self, obj):
        from organizations.models import Organization
        return OrganizationSerializerOut(Organization.objects.filter(
            memberships__user=obj, memberships__status='active'
        ), many=True, context={'request': self.context['request']}).data
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'display_name', 'avatar', 'phone_number', 'bio', 'status',
            'status_message', 'last_seen', 'is_online', 'timezone', 'password_changed',
            'email_verified', 'created_at', 'updated_at', 'organizations'
        ]


class UserProfileUpdateSerializerIn(serializers.ModelSerializer):
    """Serializer for updating user profile."""
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'avatar', 'phone_number', 'bio',
            'status_message', 'timezone'
        ]


class UserStatusUpdateSerializerIn(serializers.ModelSerializer):
    """Serializer for updating user status."""
    class Meta:
        model = User
        fields = ['status', 'status_message']


class UserPreferencesSerializerIn(serializers.ModelSerializer):
    """Serializer for user preferences input."""
    class Meta:
        model = UserPreferences
        fields = [
            'email_notifications', 'push_notifications', 'desktop_notifications',
            'notification_sound', 'show_online_status', 'allow_direct_messages',
            'theme', 'language'
        ]


class UserPreferencesSerializerOut(serializers.ModelSerializer):
    """Serializer for user preferences output."""
    class Meta:
        model = UserPreferences
        fields = [
            'id', 'email_notifications', 'push_notifications', 'desktop_notifications',
            'notification_sound', 'show_online_status', 'allow_direct_messages',
            'theme', 'language', 'created_at', 'updated_at'
        ]


class PasswordChangeSerializerIn(serializers.Serializer):
    """Serializer for password change."""
    old_password = serializers.CharField(write_only=True)
    otp = serializers.CharField()
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        user = self.context['request'].user
        decrypted_otp = decrypt_text(user.email_verification_token)
        if decrypted_otp != attrs['otp']:
            raise InvalidRequestException({"message": "Invalid OTP."})
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise InvalidRequestException({"message": "New passwords don't match."})
        if timezone.now() > user.email_verification_token_expiry:
            raise InvalidRequestException({"detail": "Verification code has expired"})

        return attrs
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise InvalidRequestException({"message": "Old password is incorrect."})
        return value


class UserSearchSerializerOut(serializers.ModelSerializer):
    """Serializer for user search results."""
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    display_name = serializers.CharField(source='get_display_name', read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'display_name', 'avatar', 'status', 'is_online', 'last_seen'
        ]


class UserListSerializerOut(serializers.ModelSerializer):
    """Serializer for user list output."""
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    display_name = serializers.CharField(source='get_display_name', read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'username', 'first_name', 'last_name', 'full_name',
            'display_name', 'avatar', 'status', 'is_online'
        ]


# Response serializers for function-based views
class UserOnlineStatusSerializerIn(serializers.Serializer):
    """Serializer for setting user online status."""
    is_online = serializers.BooleanField(default=True, help_text="Set user online/offline status")


class UserOnlineStatusSerializerOut(serializers.Serializer):
    """Serializer for user online status response."""
    message = serializers.CharField(help_text="Status message")
    status = serializers.CharField(help_text="User status")
    is_online = serializers.BooleanField(help_text="Whether user is online")


class OnlineUsersSerializerOut(serializers.Serializer):
    """Serializer for online users response."""
    online_users = UserSearchSerializerOut(many=True, help_text="List of online users")
    count = serializers.IntegerField(help_text="Number of online users")


class UserActivitySerializerOut(serializers.Serializer):
    """Serializer for user activity response."""
    user_id = serializers.UUIDField(help_text="User ID")
    user_name = serializers.CharField(help_text="User full name")
    status = serializers.CharField(help_text="User status")
    status_message = serializers.CharField(allow_null=True, help_text="User status message")
    is_online = serializers.BooleanField(help_text="Whether user is online")
    last_seen = serializers.DateTimeField(allow_null=True, help_text="Last seen timestamp")
    timezone = serializers.CharField(help_text="User timezone")
    show_status = serializers.BooleanField(help_text="Whether status is visible")


class LastActivityUpdateSerializerOut(serializers.Serializer):
    """Serializer for last activity update response."""
    message = serializers.CharField(help_text="Success message")
    last_seen = serializers.DateTimeField(help_text="Updated last seen timestamp")


class UserLogoutSerializerIn(serializers.Serializer):
    """Serializer for user logout request."""
    refresh_token = serializers.CharField(help_text="Refresh token to blacklist")


class UserLogoutSerializerOut(serializers.Serializer):
    """Serializer for user logout response."""
    message = serializers.CharField(help_text="Success message")


class PasswordChangeSerializerOut(serializers.Serializer):
    """Serializer for password change response."""
    message = serializers.CharField(help_text="Success message")


class RequestOneTimeTokenSerializerIn(serializers.Serializer):
    email = serializers.EmailField()

    def create(self, validated_data):
        email = str(validated_data.get("email")).lower()

        verification_token = generate_random_otp(6)

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise InvalidRequestException({"message": "User with this email is not found"})

        user.email_verification_token = encrypt_text(verification_token)
        user.email_verification_token_expiry = get_next_minute(datetime.datetime.now(), 15)
        user.save()

        # Send email verification token
        user_name = user.get_full_name() or user.username
        send_verification_token_email(
            recipient_email=email,
            verification_token=verification_token,
            user_name=user_name
        )

        detail = {"detail": "Verification code sent to your email"}
        if settings.DEBUG:
            detail["verification_code"] = verification_token

        return detail


class VerifyOneTimeTokenSerializerIn(serializers.Serializer):
    email = serializers.EmailField()
    verification_code = serializers.CharField()

    def create(self, validated_data):
        email = str(validated_data.get("email")).lower()
        verification_token = validated_data.get("verification_code")

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise InvalidRequestException({"detail": "User with this email is not found"})

        if verification_token != decrypt_text(user.email_verification_token):
            raise InvalidRequestException({"detail": "You have provided an invalid verification code"})

        if timezone.now() > user.email_verification_token_expiry:
            raise InvalidRequestException({"detail": "Verification code has expired"})

        user.email_verified = True
        user.save()

        return {"detail": "Verification code is valid"}


