# Generated by Django 5.2.5 on 2025-08-29 08:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_user_email_verification_token_expiry_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='avatar',
        ),
        migrations.AddField(
            model_name='user',
            name='avatar_url',
            field=models.TextField(blank=True, help_text="URL to user's profile picture stored in cloud storage", null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='password_changed',
            field=models.Bo<PERSON>anField(default=False, help_text='Whether user has changed password after signup'),
        ),
    ]
