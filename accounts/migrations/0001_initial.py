# Generated by Django 5.2.5 on 2025-08-08 08:28

import django.contrib.auth.models
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('email', models.EmailField(help_text="User's email address", max_length=254, unique=True)),
                ('first_name', models.CharField(help_text="User's first name", max_length=150)),
                ('last_name', models.CharField(help_text="User's last name", max_length=150)),
                ('avatar', models.ImageField(blank=True, help_text="User's profile picture", null=True, upload_to='avatars/')),
                ('phone_number', models.CharField(blank=True, help_text="User's phone number", max_length=20, null=True)),
                ('bio', models.TextField(blank=True, help_text="User's bio/description", max_length=500, null=True)),
                ('status', models.CharField(choices=[('online', 'Online'), ('away', 'Away'), ('busy', 'Busy'), ('offline', 'Offline'), ('do_not_disturb', 'Do Not Disturb')], default='offline', help_text="User's current status", max_length=20)),
                ('status_message', models.CharField(blank=True, help_text='Custom status message', max_length=200, null=True)),
                ('last_seen', models.DateTimeField(blank=True, help_text='Last time user was active', null=True)),
                ('is_online', models.BooleanField(default=False, help_text='Whether user is currently online')),
                ('timezone', models.CharField(default='UTC', help_text="User's timezone", max_length=50)),
                ('email_verified', models.BooleanField(default=False, help_text="Whether user's email is verified")),
                ('email_verification_token', models.CharField(blank=True, help_text='Token for email verification', max_length=255, null=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
                'db_table': 'accounts_user',
                'ordering': ['-created_at'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='UserPreferences',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('email_notifications', models.BooleanField(default=True, help_text='Enable email notifications')),
                ('push_notifications', models.BooleanField(default=True, help_text='Enable push notifications')),
                ('desktop_notifications', models.BooleanField(default=True, help_text='Enable desktop notifications')),
                ('notification_sound', models.BooleanField(default=True, help_text='Enable notification sounds')),
                ('show_online_status', models.BooleanField(default=True, help_text='Show online status to others')),
                ('allow_direct_messages', models.BooleanField(default=True, help_text='Allow direct messages from anyone')),
                ('theme', models.CharField(choices=[('light', 'Light'), ('dark', 'Dark'), ('auto', 'Auto')], default='auto', help_text='UI theme preference', max_length=20)),
                ('language', models.CharField(default='en', help_text='Preferred language', max_length=10)),
                ('user', models.OneToOneField(help_text='User associated with these preferences', on_delete=django.db.models.deletion.CASCADE, related_name='preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Preferences',
                'verbose_name_plural': 'User Preferences',
                'db_table': 'accounts_user_preferences',
            },
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('session_key', models.CharField(help_text='Django session key', max_length=40, unique=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address of the session', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='User agent string', null=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether session is active')),
                ('last_activity', models.DateTimeField(auto_now=True, help_text='Last activity timestamp')),
                ('user', models.ForeignKey(help_text='User associated with this session', on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Session',
                'verbose_name_plural': 'User Sessions',
                'db_table': 'accounts_user_session',
                'ordering': ['-last_activity'],
            },
        ),
    ]
