from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from accounts.models import User, UserSession


class Command(BaseCommand):
    help = 'Clean up inactive user sessions and update user online status'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--inactive-minutes',
            type=int,
            default=30,
            help='Minutes of inactivity before marking user as offline (default: 30)'
        )
        parser.add_argument(
            '--cleanup-sessions',
            action='store_true',
            help='Clean up old inactive sessions'
        )
    
    def handle(self, *args, **options):
        inactive_minutes = options['inactive_minutes']
        cleanup_sessions = options['cleanup_sessions']
        
        # Calculate cutoff time
        cutoff_time = timezone.now() - timedelta(minutes=inactive_minutes)
        
        # Find users who should be marked as offline
        inactive_users = User.objects.filter(
            is_online=True,
            last_seen__lt=cutoff_time
        )
        
        count = 0
        for user in inactive_users:
            user.set_online_status(False)
            count += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Marked {count} users as offline due to inactivity'
            )
        )
        
        # Clean up old sessions if requested
        if cleanup_sessions:
            old_sessions = UserSession.objects.filter(
                last_activity__lt=cutoff_time,
                is_active=True
            )
            session_count = old_sessions.count()
            old_sessions.update(is_active=False)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Cleaned up {session_count} inactive sessions'
                )
            )
