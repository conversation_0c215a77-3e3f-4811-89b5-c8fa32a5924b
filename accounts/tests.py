from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from .models import UserPreferences

User = get_user_model()


class UserModelTest(TestCase):
    """Test cases for User model."""

    def setUp(self):
        self.user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123'
        }

    def test_create_user(self):
        """Test creating a user."""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(user.email, self.user_data['email'])
        self.assertEqual(user.username, self.user_data['username'])
        self.assertTrue(user.check_password(self.user_data['password']))
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)

    def test_create_superuser(self):
        """Test creating a superuser."""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpass123',
            username='admin',
            first_name='Admin',
            last_name='User'
        )
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)

    def test_user_string_representation(self):
        """Test user string representation."""
        user = User.objects.create_user(**self.user_data)
        expected = f"{user.get_full_name()} ({user.email})"
        self.assertEqual(str(user), expected)

    def test_user_preferences_created(self):
        """Test that user preferences are created automatically."""
        user = User.objects.create_user(**self.user_data)
        self.assertTrue(hasattr(user, 'preferences'))
        self.assertIsInstance(user.preferences, UserPreferences)


class UserAPITest(APITestCase):
    """Test cases for User API endpoints."""

    def setUp(self):
        self.user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'testpass123',
            'password_confirm': 'testpass123'
        }
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='existing',
            first_name='Existing',
            last_name='User',
            password='existingpass123'
        )

    def test_user_registration(self):
        """Test user registration."""
        url = reverse('accounts:register')
        response = self.client.post(url, self.user_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('access_token', response.data)
        self.assertIn('refresh_token', response.data)
        self.assertTrue(User.objects.filter(email=self.user_data['email']).exists())

    def test_user_registration_password_mismatch(self):
        """Test user registration with password mismatch."""
        url = reverse('accounts:register')
        data = self.user_data.copy()
        data['password_confirm'] = 'different_password'

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_user_login(self):
        """Test user login."""
        url = reverse('accounts:login')
        data = {
            'email': self.user.email,
            'password': 'existingpass123'
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access_token', response.data)
        self.assertIn('refresh_token', response.data)

    def test_user_login_invalid_credentials(self):
        """Test user login with invalid credentials."""
        url = reverse('accounts:login')
        data = {
            'email': self.user.email,
            'password': 'wrongpassword'
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_get_user_profile(self):
        """Test getting user profile."""
        self.client.force_authenticate(user=self.user)
        url = reverse('accounts:profile')

        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], self.user.email)

    def test_update_user_profile(self):
        """Test updating user profile."""
        self.client.force_authenticate(user=self.user)
        url = reverse('accounts:profile')
        data = {
            'first_name': 'Updated',
            'bio': 'Updated bio'
        }

        response = self.client.patch(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['first_name'], 'Updated')

        # Verify in database
        self.user.refresh_from_db()
        self.assertEqual(self.user.first_name, 'Updated')
        self.assertEqual(self.user.bio, 'Updated bio')

    def test_user_search(self):
        """Test user search."""
        # Create additional users
        User.objects.create_user(
            email='<EMAIL>',
            username='john',
            first_name='John',
            last_name='Doe',
            password='pass123'
        )

        self.client.force_authenticate(user=self.user)
        url = reverse('accounts:search')

        response = self.client.get(url, {'q': 'john'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_user_preferences(self):
        """Test user preferences management."""
        self.client.force_authenticate(user=self.user)
        url = reverse('accounts:preferences')

        # Get preferences
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Update preferences
        data = {
            'email_notifications': False,
            'theme': 'dark'
        }
        response = self.client.patch(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email_notifications'], False)
        self.assertEqual(response.data['theme'], 'dark')
